#!/usr/bin/env node

/**
 * SynapseAI Project Download Creator
 * Creates a downloadable package of the entire project
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 SynapseAI Download Package Creator');
console.log('=====================================\n');

const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
const packageName = `synapseai-${timestamp}`;
const archiveName = `${packageName}.zip`;

// Files and directories to exclude
const excludePatterns = [
  'node_modules',
  '.git',
  '.next',
  'dist',
  'build',
  '.env.local',
  '.env.production',
  '*.log',
  '.DS_Store',
  'coverage',
  '.nyc_output',
  '*.tgz',
  '*.tar.gz',
  '*.zip'
];

console.log('📦 Creating project package...');

try {
  // Create exclude pattern for zip command
  const excludeArgs = excludePatterns.map(pattern => `-x "${pattern}"`).join(' ');
  
  // Create zip archive
  const zipCommand = `zip -r "${archiveName}" . ${excludeArgs}`;
  console.log('🔄 Compressing files...');
  execSync(zipCommand, { stdio: 'inherit' });
  
  // Get file size
  const stats = fs.statSync(archiveName);
  const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
  
  console.log('\n✅ Download package created successfully!');
  console.log(`📁 File: ${archiveName}`);
  console.log(`📏 Size: ${fileSizeInMB} MB`);
  
  console.log('\n📋 Package includes:');
  console.log('   ✓ Complete SynapseAI source code');
  console.log('   ✓ Frontend (Next.js) and Backend (NestJS)');
  console.log('   ✓ All components and UI elements');
  console.log('   ✓ Configuration files');
  console.log('   ✓ Documentation');
  console.log('   ✓ Package.json with all dependencies');
  
  console.log('\n🚀 To use this package:');
  console.log(`   1. Download: ${archiveName}`);
  console.log(`   2. Extract the ZIP file`);
  console.log(`   3. Navigate to the extracted folder`);
  console.log(`   4. Run: npm install`);
  console.log(`   5. Set up .env.local with your credentials`);
  console.log(`   6. Run: npm run dev`);
  
  console.log('\n🌟 Happy coding with SynapseAI!');
  
} catch (error) {
  console.error('❌ Error creating package:', error.message);
  
  // Fallback: Create a simple copy-based package
  console.log('\n🔄 Trying alternative method...');
  
  try {
    const copyCommand = `mkdir -p ${packageName} && cp -r . ${packageName}/ && rm -rf ${packageName}/node_modules ${packageName}/.git ${packageName}/.next && zip -r ${archiveName} ${packageName} && rm -rf ${packageName}`;
    execSync(copyCommand, { stdio: 'inherit' });
    
    const stats = fs.statSync(archiveName);
    const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
    
    console.log(`✅ Alternative package created: ${archiveName} (${fileSizeInMB} MB)`);
  } catch (fallbackError) {
    console.error('❌ Fallback method also failed:', fallbackError.message);
    console.log('\n💡 Manual download instructions:');
    console.log('   1. Copy all project files to a new folder');
    console.log('   2. Exclude: node_modules, .git, .next, dist, build');
    console.log('   3. Create a ZIP archive of the remaining files');
  }
}
