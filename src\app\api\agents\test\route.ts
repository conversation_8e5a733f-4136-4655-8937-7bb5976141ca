// Production API Route for Agent Testing
// Real AI provider integration with comprehensive monitoring and cost tracking

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { aiProviders } from "@/lib/ai-providers";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface TestAgentRequest {
  agentId?: string; // Optional - for testing existing agents
  agentConfig?: {
    name: string;
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
  }; // Optional - for testing new agent configurations
  input: string;
  metadata?: Record<string, any>;
}

interface TestAgentResponse {
  success: boolean;
  data?: {
    executionId: string;
    output: string;
    usage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
    cost: number;
    latency: number;
    provider: string;
    model: string;
    sessionId: string;
  };
  error?: string;
}

// POST /api/agents/test - Test agent with real AI provider
export async function POST(
  request: NextRequest,
): Promise<NextResponse<TestAgentResponse>> {
  const startTime = Date.now();
  let executionId: string | null = null;
  let sessionId: string | null = null;

  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { success: false, error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (
      !hasPermission(payload.role, payload.permissions, "agents", "execute")
    ) {
      return NextResponse.json(
        { success: false, error: "Insufficient permissions to test agents" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body: TestAgentRequest = await request.json();

    if (!body.input?.trim()) {
      return NextResponse.json(
        { success: false, error: "Input is required for testing" },
        { status: 400 },
      );
    }

    if (!body.agentId && !body.agentConfig) {
      return NextResponse.json(
        { success: false, error: "Either agentId or agentConfig is required" },
        { status: 400 },
      );
    }

    // Get agent configuration
    let agentConfig: {
      name: string;
      provider: string;
      model: string;
      temperature: number;
      maxTokens: number;
      systemPrompt: string;
    };

    if (body.agentId) {
      // Testing existing agent
      const agent = await db.getAgent(body.agentId, payload.org);
      if (!agent) {
        return NextResponse.json(
          { success: false, error: "Agent not found" },
          { status: 404 },
        );
      }
      agentConfig = {
        name: agent.name,
        provider: agent.provider,
        model: agent.model,
        temperature: agent.temperature,
        maxTokens: agent.maxTokens,
        systemPrompt: agent.systemPrompt,
      };
    } else {
      // Testing new agent configuration
      agentConfig = body.agentConfig!;
    }

    // Validate provider availability
    if (!aiProviders.isProviderAvailable(agentConfig.provider)) {
      return NextResponse.json(
        {
          success: false,
          error: `Provider ${agentConfig.provider} is not available`,
        },
        { status: 503 },
      );
    }

    // Create session for this test execution
    const sessionManager = getSessionManager(payload.org);
    sessionId = await sessionManager.createSession(
      "agents",
      "agent",
      {
        action: "test",
        agentId: body.agentId,
        agentConfig,
        input: body.input,
        startTime: new Date().toISOString(),
      },
      {
        tags: ["api", "agents", "test", "execution"],
        conversationId: `test-${Date.now()}`,
      },
    );

    // Create execution record
    const executionData = {
      agentId: body.agentId || "test-agent",
      organizationId: payload.org,
      userId: payload.sub,
      input: body.input,
      output: "", // Will be updated after execution
      status: "running" as const,
      cost: 0,
      tokens: {
        input: 0,
        output: 0,
        total: 0,
      },
      metadata: {
        ...body.metadata,
        sessionId,
        testMode: true,
        agentConfig,
      },
    };

    const execution = await db.createExecution(executionData);
    executionId = execution.id;

    // Send test start event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-test-start-${executionId}`,
        type: "test_started",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          executionId,
          agentId: body.agentId,
          agentConfig,
          input: body.input,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX start event:", apixError);
    }

    // Prepare messages for AI provider
    const messages = [
      {
        role: "system" as const,
        content: agentConfig.systemPrompt,
      },
      {
        role: "user" as const,
        content: body.input,
      },
    ];

    // Execute with AI provider
    const aiRequest = {
      provider: agentConfig.provider,
      model: agentConfig.model,
      messages,
      temperature: agentConfig.temperature,
      maxTokens: agentConfig.maxTokens,
      organizationId: payload.org,
      userId: payload.sub,
    };

    const aiResponse = await aiProviders.generateResponse(aiRequest);
    const totalLatency = Date.now() - startTime;

    // Update execution record with results
    const updatedExecution = await db.updateExecution(
      executionId,
      payload.org,
      {
        output: aiResponse.content,
        status: "completed",
        duration: totalLatency,
        cost: aiResponse.cost,
        tokens: {
          input: aiResponse.usage.promptTokens,
          output: aiResponse.usage.completionTokens,
          total: aiResponse.usage.totalTokens,
        },
        metadata: {
          ...executionData.metadata,
          finishReason: aiResponse.finishReason,
          providerLatency: aiResponse.latency,
          totalLatency,
        },
      },
    );

    // Update session with results
    await sessionManager.updateSession(sessionId, {
      output: aiResponse.content,
      cost: aiResponse.cost,
      latency: totalLatency,
      tokens: aiResponse.usage,
      endTime: new Date().toISOString(),
      success: true,
    });

    // Update agent usage statistics if testing existing agent
    if (body.agentId) {
      try {
        await db.updateAgentUsage(body.agentId, payload.org, aiResponse.cost);
      } catch (usageError) {
        console.warn("Failed to update agent usage:", usageError);
      }
    }

    // Send test completion event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-test-complete-${executionId}`,
        type: "tested",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          executionId,
          agentId: body.agentId,
          output: aiResponse.content,
          usage: aiResponse.usage,
          cost: aiResponse.cost,
          latency: totalLatency,
          success: true,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX completion event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        output: aiResponse.content,
        usage: aiResponse.usage,
        cost: aiResponse.cost,
        latency: totalLatency,
        provider: aiResponse.provider,
        model: aiResponse.model,
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error testing agent:", error);

    // Update execution record with error if it was created
    if (executionId) {
      try {
        await db.updateExecution(executionId, payload!.org, {
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error",
          duration: Date.now() - startTime,
        });
      } catch (updateError) {
        console.error("Failed to update execution with error:", updateError);
      }
    }

    // Update session with error if it was created
    if (sessionId && payload) {
      try {
        const sessionManager = getSessionManager(payload.org);
        await sessionManager.updateSession(sessionId, {
          error: error instanceof Error ? error.message : "Unknown error",
          endTime: new Date().toISOString(),
          success: false,
        });
      } catch (sessionError) {
        console.error("Failed to update session with error:", sessionError);
      }
    }

    // Send test error event via APIX
    if (payload) {
      try {
        const apixClient = getAPXClient(
          payload.org,
          payload.sub,
          request.headers.get("authorization")!.substring(7),
        );
        await apixClient.sendEvent({
          id: `agent-test-error-${executionId || Date.now()}`,
          type: "test_error",
          module: "agents",
          organizationId: payload.org,
          userId: payload.sub,
          timestamp: new Date().toISOString(),
          data: {
            executionId,
            error: error instanceof Error ? error.message : "Unknown error",
            sessionId,
          },
          version: 1,
        });
      } catch (apixError) {
        console.warn("Failed to send APIX error event:", apixError);
      }
    }

    // Determine appropriate error response
    if (error instanceof Error) {
      if (error.message.includes("Rate limit")) {
        return NextResponse.json(
          {
            success: false,
            error: "Rate limit exceeded. Please try again later.",
          },
          { status: 429 },
        );
      }
      if (error.message.includes("Circuit breaker")) {
        return NextResponse.json(
          {
            success: false,
            error: "Service temporarily unavailable. Please try again later.",
          },
          { status: 503 },
        );
      }
      if (error.message.includes("not available")) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 503 },
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 },
    );
  }
}
