"use client";

import React from "react";
import {
  EdgeProps,
  getB<PERSON>ier<PERSON>ath,
  EdgeLabelRenderer,
} from "@/lib/mock-reactflow";
import { X, CheckCircle, XCircle, Clock, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface CustomEdgeData {
  label?: string;
  condition?: string;
  status?: "idle" | "active" | "completed" | "failed";
  animated?: boolean;
  executionTime?: number;
  onDelete?: (edgeId: string) => void;
}

const CustomEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  selected,
}: EdgeProps<CustomEdgeData>) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const getStatusColor = () => {
    switch (data?.status) {
      case "active":
        return "#3b82f6"; // blue
      case "completed":
        return "#10b981"; // green
      case "failed":
        return "#ef4444"; // red
      default:
        return "#6b7280"; // gray
    }
  };

  const getStatusIcon = () => {
    switch (data?.status) {
      case "active":
        return <Clock className="h-3 w-3 text-blue-600 animate-spin" />;
      case "completed":
        return <CheckCircle className="h-3 w-3 text-green-600" />;
      case "failed":
        return <XCircle className="h-3 w-3 text-red-600" />;
      default:
        return null;
    }
  };

  const strokeColor = selected ? "#3b82f6" : getStatusColor();
  const strokeWidth = selected ? 3 : 2;

  return (
    <>
      <path
        id={id}
        style={{
          ...style,
          strokeWidth,
          stroke: strokeColor,
          fill: "none",
          strokeDasharray: data?.animated ? "5,5" : "none",
          animation: data?.animated ? "dash 1s linear infinite" : "none",
        }}
        className={cn(
          "react-flow__edge-path transition-all duration-200",
          data?.status === "active" && "animate-pulse",
        )}
        d={edgePath}
        markerEnd={markerEnd}
      />

      {/* Edge Label */}
      {(data?.label || data?.condition || data?.status !== "idle") && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: "absolute",
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 12,
              pointerEvents: "all",
            }}
            className="nodrag nopan"
          >
            <div
              className={cn(
                "bg-white border rounded px-2 py-1 shadow-sm flex items-center gap-2 max-w-48",
                selected
                  ? "border-blue-300 ring-1 ring-blue-200"
                  : "border-gray-200",
              )}
            >
              {/* Status Icon */}
              {getStatusIcon()}

              {/* Label or Condition */}
              {(data?.label || data?.condition) && (
                <span className="text-xs text-gray-600 truncate">
                  {data.label || data.condition}
                </span>
              )}

              {/* Status Badge */}
              {data?.status && data.status !== "idle" && (
                <Badge
                  variant={
                    data.status === "completed"
                      ? "default"
                      : data.status === "failed"
                        ? "destructive"
                        : data.status === "active"
                          ? "secondary"
                          : "outline"
                  }
                  className="text-xs px-1 py-0"
                >
                  {data.status}
                </Badge>
              )}

              {/* Execution Time */}
              {data?.executionTime && (
                <span className="text-xs text-muted-foreground">
                  {data.executionTime}ms
                </span>
              )}

              {/* Animated Indicator */}
              {data?.animated && <Zap className="h-3 w-3 text-yellow-500" />}

              {/* Delete Button */}
              {data?.onDelete && selected && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-red-100 ml-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    data.onDelete?.(id);
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </EdgeLabelRenderer>
      )}

      {/* Add CSS for dash animation */}
      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -10;
          }
        }
      `}</style>
    </>
  );
};

export default CustomEdge;
