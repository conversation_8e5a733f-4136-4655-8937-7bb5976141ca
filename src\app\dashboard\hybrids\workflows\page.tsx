"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Play,
  Edit,
  Copy,
  Trash,
  Eye,
  Settings,
  Activity,
  Clock,
  DollarSign,
  CheckCircle,
  XCircle,
  Pause,
  ArrowLeft,
} from 'lucide-react';

import { mockAuthContext } from '@/lib/auth-context';

interface HybridWorkflow {
  id: string;
  name: string;
  description: string;
  status: "draft" | "active" | "paused" | "error";
  provider: string;
  nodes: number;
  connections: number;
  metrics: {
    executions: number;
    success_rate: number;
    avg_cost: number;
    avg_latency: number;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  isPublic: boolean;
}

export default function WorkflowsPage() {
  const router = useRouter();
  const [workflows, setWorkflows] = useState<HybridWorkflow[]>([]);
  const [filteredWorkflows, setFilteredWorkflows] = useState<HybridWorkflow[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);

  // Auth & RBAC
  const { user, hasPermission } = mockAuthContext;
  const canCreateWorkflow = hasPermission("hybrids", "create");
  const canEditWorkflow = hasPermission("hybrids", "update");
  const canExecuteWorkflow = hasPermission("hybrids", "execute");
  const canDeleteWorkflow = hasPermission("hybrids", "delete");

  // Load workflows
  useEffect(() => {
    loadWorkflows();
  }, []);

  // Filter workflows based on search and status
  useEffect(() => {
    let filtered = workflows;

    if (searchTerm) {
      filtered = filtered.filter(workflow =>
        workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(workflow => workflow.status === statusFilter);
    }

    setFilteredWorkflows(filtered);
  }, [workflows, searchTerm, statusFilter]);

  const loadWorkflows = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockWorkflows: HybridWorkflow[] = [
        {
          id: "workflow-1",
          name: "Content Generation Pipeline",
          description: "Multi-step content creation with review and optimization",
          status: "active",
          provider: "OpenAI GPT-4",
          nodes: 5,
          connections: 4,
          metrics: {
            executions: 156,
            success_rate: 94.2,
            avg_cost: 0.12,
            avg_latency: 2400,
          },
          createdAt: "2024-01-15T10:30:00Z",
          updatedAt: "2024-01-20T14:22:00Z",
          createdBy: user?.id || "user-123",
          isPublic: false,
        },
        {
          id: "workflow-2",
          name: "Code Review Assistant",
          description: "Automated code analysis and suggestions with human oversight",
          status: "active",
          provider: "Claude 3 Sonnet",
          nodes: 3,
          connections: 2,
          metrics: {
            executions: 89,
            success_rate: 97.8,
            avg_cost: 0.08,
            avg_latency: 1800,
          },
          createdAt: "2024-01-10T09:15:00Z",
          updatedAt: "2024-01-18T16:45:00Z",
          createdBy: user?.id || "user-123",
          isPublic: true,
        },
        {
          id: "workflow-3",
          name: "Customer Support Bot",
          description: "Intelligent customer support with escalation to human agents",
          status: "draft",
          provider: "Gemini Pro",
          nodes: 7,
          connections: 8,
          metrics: {
            executions: 0,
            success_rate: 0,
            avg_cost: 0,
            avg_latency: 0,
          },
          createdAt: "2024-01-22T11:00:00Z",
          updatedAt: "2024-01-22T11:00:00Z",
          createdBy: user?.id || "user-123",
          isPublic: false,
        },
      ];
      setWorkflows(mockWorkflows);
    } catch (error) {
      console.error("Failed to load workflows:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleExecuteWorkflow = async (workflowId: string) => {
    if (!canExecuteWorkflow) return;
    
    setLoading(true);
    try {
      // Mock execution - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log("Executing workflow:", workflowId);
    } catch (error) {
      console.error("Failed to execute workflow:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteWorkflow = async (workflowId: string) => {
    if (!canDeleteWorkflow) return;
    
    if (confirm("Are you sure you want to delete this workflow?")) {
      setWorkflows(prev => prev.filter(w => w.id !== workflowId));
    }
  };

  const handleDuplicateWorkflow = async (workflowId: string) => {
    if (!canCreateWorkflow) return;
    
    const workflow = workflows.find(w => w.id === workflowId);
    if (workflow) {
      const duplicated = {
        ...workflow,
        id: `${workflow.id}-copy`,
        name: `${workflow.name} (Copy)`,
        status: "draft" as const,
        metrics: {
          executions: 0,
          success_rate: 0,
          avg_cost: 0,
          avg_latency: 0,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      setWorkflows(prev => [...prev, duplicated]);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "paused":
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "default",
      paused: "secondary",
      error: "destructive",
      draft: "outline",
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/hybrids')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Hybrids
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Hybrid Workflows
            </h1>
            <p className="text-gray-600 mt-1">
              Manage your AI-powered hybrid workflows
            </p>
          </div>
        </div>
        <Button 
          onClick={() => router.push('/dashboard/hybrids/create')}
          disabled={!canCreateWorkflow}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Workflow
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="error">Error</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Workflows Table */}
      <Card>
        <CardHeader>
          <CardTitle>Workflows ({filteredWorkflows.length})</CardTitle>
          <CardDescription>
            Manage and monitor your hybrid AI workflows
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Nodes</TableHead>
                <TableHead>Executions</TableHead>
                <TableHead>Success Rate</TableHead>
                <TableHead>Avg Cost</TableHead>
                <TableHead>Updated</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredWorkflows.map((workflow) => (
                <TableRow key={workflow.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{workflow.name}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {workflow.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(workflow.status)}
                      {getStatusBadge(workflow.status)}
                    </div>
                  </TableCell>
                  <TableCell>{workflow.provider}</TableCell>
                  <TableCell>{workflow.nodes}</TableCell>
                  <TableCell>{workflow.metrics.executions}</TableCell>
                  <TableCell>{workflow.metrics.success_rate}%</TableCell>
                  <TableCell>${workflow.metrics.avg_cost}</TableCell>
                  <TableCell>
                    {new Date(workflow.updatedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => router.push(`/dashboard/hybrids/create?id=${workflow.id}&mode=view`)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </DropdownMenuItem>
                        {canEditWorkflow && (
                          <DropdownMenuItem
                            onClick={() => router.push(`/dashboard/hybrids/edit/${workflow.id}`)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {canExecuteWorkflow && workflow.status === 'active' && (
                          <DropdownMenuItem
                            onClick={() => handleExecuteWorkflow(workflow.id)}
                          >
                            <Play className="h-4 w-4 mr-2" />
                            Execute
                          </DropdownMenuItem>
                        )}
                        {canCreateWorkflow && (
                          <DropdownMenuItem
                            onClick={() => handleDuplicateWorkflow(workflow.id)}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                        )}
                        {canDeleteWorkflow && (
                          <DropdownMenuItem
                            onClick={() => handleDeleteWorkflow(workflow.id)}
                            className="text-red-600"
                          >
                            <Trash className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
