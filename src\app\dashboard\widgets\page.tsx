"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Grid3X3,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  Bot,
  Wrench,
  Network,
  Globe,
} from "lucide-react";
import { mockAuthContext } from "@/lib/auth-context";
import WidgetGenerator from "@/components/widgets/WidgetGenerator";

interface Widget {
  id: string;
  name: string;
  type: "agent" | "tool" | "hybrid";
  sourceId: string;
  sourceName: string;
  status: "active" | "draft" | "disabled";
  embedType: "script" | "iframe" | "plugin";
  views: number;
  lastUsed: string;
  theme: "light" | "dark" | "auto";
}

export default function WidgetsPage() {
  const [activeTab, setActiveTab] = useState("widgets");
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const { hasPermission } = mockAuthContext;

  const canCreateWidgets = hasPermission("widgets", "create");
  const canUpdateWidgets = hasPermission("widgets", "update");
  const canDeleteWidgets = hasPermission("widgets", "delete");

  const widgets: Widget[] = [
    {
      id: "widget_1",
      name: "Customer Support Chat",
      type: "agent",
      sourceId: "agent_1",
      sourceName: "Customer Support Agent",
      status: "active",
      embedType: "script",
      views: 1247,
      lastUsed: "2024-01-15T11:30:00Z",
      theme: "light",
    },
    {
      id: "widget_2",
      name: "Email Subscription Form",
      type: "tool",
      sourceId: "tool_1",
      sourceName: "Email Sender",
      status: "active",
      embedType: "iframe",
      views: 856,
      lastUsed: "2024-01-15T10:15:00Z",
      theme: "dark",
    },
    {
      id: "widget_3",
      name: "Lead Qualification Bot",
      type: "hybrid",
      sourceId: "hybrid_1",
      sourceName: "Sales Workflow",
      status: "draft",
      embedType: "script",
      views: 0,
      lastUsed: "2024-01-14T16:45:00Z",
      theme: "auto",
    },
    {
      id: "widget_4",
      name: "Document Analyzer",
      type: "tool",
      sourceId: "tool_2",
      sourceName: "PDF Parser",
      status: "disabled",
      embedType: "plugin",
      views: 342,
      lastUsed: "2024-01-13T09:20:00Z",
      theme: "light",
    },
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "agent":
        return Bot;
      case "tool":
        return Wrench;
      case "hybrid":
        return Network;
      default:
        return Grid3X3;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "draft":
        return "bg-yellow-500";
      case "disabled":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const generateEmbedCode = (widget: Widget) => {
    switch (widget.embedType) {
      case "script":
        return `<script src="https://synapseai.com/embed/${widget.id}.js" data-theme="${widget.theme}"></script>`;
      case "iframe":
        return `<iframe src="https://synapseai.com/widget/${widget.id}" width="400" height="600" frameborder="0"></iframe>`;
      case "plugin":
        return `[synapseai widget="${widget.id}" theme="${widget.theme}"]`;
      default:
        return "";
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Widgets</h1>
          <p className="text-muted-foreground">
            Create embeddable AI components for websites and applications
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {canCreateWidgets && (
            <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Widget
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Create New Widget</DialogTitle>
                  <DialogDescription>
                    Turn your agents, tools, or hybrids into embeddable widgets
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="widget-name">Widget Name</Label>
                    <Input id="widget-name" placeholder="Enter widget name" />
                  </div>

                  <div>
                    <Label htmlFor="source-type">Source Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select source type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="agent">AI Agent</SelectItem>
                        <SelectItem value="tool">Tool</SelectItem>
                        <SelectItem value="hybrid">Hybrid Workflow</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="source-id">Source</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select source" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="agent_1">Customer Support Agent</SelectItem>
                        <SelectItem value="tool_1">Email Sender</SelectItem>
                        <SelectItem value="hybrid_1">Sales Workflow</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="embed-type">Embed Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select embed type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="script">JavaScript Embed</SelectItem>
                        <SelectItem value="iframe">iFrame Embed</SelectItem>
                        <SelectItem value="plugin">CMS Plugin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="theme">Theme</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select theme" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="auto">Auto</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={() => setIsCreateOpen(false)}>
                      Create Widget
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Widgets</CardTitle>
            <Grid3X3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{widgets.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Widgets</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {widgets.filter(w => w.status === "active").length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {widgets.reduce((acc, w) => acc + w.views, 0).toLocaleString()}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(widgets.reduce((acc, w) => acc + w.views, 0) / widgets.length)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="widgets">My Widgets</TabsTrigger>
          <TabsTrigger value="generator">Widget Generator</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="widgets" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Widget Management</CardTitle>
                  <CardDescription>
                    Manage your embeddable AI widgets
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search widgets..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8 w-[250px]"
                    />
                  </div>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {widgets.map((widget) => {
                  const TypeIcon = getTypeIcon(widget.type);
                  return (
                    <Card key={widget.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <TypeIcon className="h-4 w-4" />
                            <span className="font-medium">{widget.name}</span>
                          </div>
                          <Badge variant="outline" className="flex items-center space-x-1">
                            <div className={`h-2 w-2 rounded-full ${getStatusColor(widget.status)}`} />
                            <span className="capitalize">{widget.status}</span>
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2 text-sm text-muted-foreground">
                          <div className="flex justify-between">
                            <span>Source:</span>
                            <span>{widget.sourceName}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Type:</span>
                            <span className="capitalize">{widget.embedType}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Views:</span>
                            <span>{widget.views.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Theme:</span>
                            <span className="capitalize">{widget.theme}</span>
                          </div>
                        </div>
                        
                        <div className="mt-4 p-2 bg-muted rounded text-xs font-mono">
                          {generateEmbedCode(widget)}
                        </div>

                        <div className="flex justify-between mt-4">
                          <div className="flex space-x-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                            {canUpdateWidgets && (
                              <Button variant="ghost" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm">
                              <Copy className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                          </div>
                          {canDeleteWidgets && (
                            <Button variant="ghost" size="sm" className="text-red-600">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="generator" className="space-y-4">
          <WidgetGenerator
            sourceType="agent"
            sourceId="agent_1"
            sourceName="Customer Support Agent"
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Widget Analytics</CardTitle>
              <CardDescription>
                Track performance and usage of your widgets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Grid3X3 className="h-12 w-12 mx-auto mb-4" />
                <p>Widget analytics coming soon...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
