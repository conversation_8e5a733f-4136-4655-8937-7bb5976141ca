"use client";

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';

// Types
export interface XYPosition {
  x: number;
  y: number;
}

export interface Node<T = any> {
  id: string;
  position: XYPosition;
  data: T;
  type?: string;
  selected?: boolean;
  dragging?: boolean;
}

export interface Edge<T = any> {
  id: string;
  source: string;
  target: string;
  type?: string;
  data?: T;
  selected?: boolean;
}

export interface Connection {
  source: string;
  target: string;
  sourceHandle?: string | null;
  targetHandle?: string | null;
}

export enum Position {
  Left = 'left',
  Top = 'top',
  Right = 'right',
  Bottom = 'bottom',
}

export enum ConnectionMode {
  Strict = 'strict',
  Loose = 'loose',
}

export interface NodeProps<T = any> {
  id: string;
  data: T;
  selected?: boolean;
}

export interface EdgeProps<T = any> {
  id: string;
  source: string;
  target: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: Position;
  targetPosition: Position;
  style?: React.CSSProperties;
  data?: T;
  markerEnd?: string;
}

export type NodeTypes = Record<string, React.ComponentType<NodeProps<any>>>;
export type EdgeTypes = Record<string, React.ComponentType<EdgeProps<any>>>;

// Context
interface ReactFlowContextType {
  nodes: Node[];
  edges: Edge[];
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>;
  onNodesChange?: (changes: any[]) => void;
  onEdgesChange?: (changes: any[]) => void;
  onConnect?: (connection: Connection) => void;
  onNodeClick?: (event: React.MouseEvent, node: Node) => void;
  onEdgeClick?: (event: React.MouseEvent, edge: Edge) => void;
  nodeTypes?: NodeTypes;
  edgeTypes?: EdgeTypes;
  selectedNode: Node | null;
  selectedEdge: Edge | null;
  setSelectedNode: (node: Node | null) => void;
  setSelectedEdge: (edge: Edge | null) => void;
  viewport: { x: number; y: number; zoom: number };
  setViewport: (viewport: { x: number; y: number; zoom: number }) => void;
}

const ReactFlowContext = createContext<ReactFlowContextType | null>(null);

// Provider
export const ReactFlowProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [viewport, setViewport] = useState({ x: 0, y: 0, zoom: 1 });

  const value: ReactFlowContextType = {
    nodes,
    edges,
    setNodes,
    setEdges,
    selectedNode,
    selectedEdge,
    setSelectedNode,
    setSelectedEdge,
    viewport,
    setViewport,
  };

  return (
    <ReactFlowContext.Provider value={value}>
      {children}
    </ReactFlowContext.Provider>
  );
};

// Hooks
export function useNodesState(initialNodes: Node[]): [Node[], React.Dispatch<React.SetStateAction<Node[]>>, (changes: any[]) => void] {
  const [nodes, setNodes] = useState<Node[]>(initialNodes);
  
  const onNodesChange = useCallback((changes: any[]) => {
    setNodes(currentNodes => {
      return currentNodes.map(node => {
        const change = changes.find(c => c.id === node.id);
        if (change) {
          switch (change.type) {
            case 'position':
              return { ...node, position: change.position };
            case 'select':
              return { ...node, selected: change.selected };
            case 'remove':
              return null;
            default:
              return node;
          }
        }
        return node;
      }).filter(Boolean) as Node[];
    });
  }, []);

  return [nodes, setNodes, onNodesChange];
}

export function useEdgesState(initialEdges: Edge[]): [Edge[], React.Dispatch<React.SetStateAction<Edge[]>>, (changes: any[]) => void] {
  const [edges, setEdges] = useState<Edge[]>(initialEdges);
  
  const onEdgesChange = useCallback((changes: any[]) => {
    setEdges(currentEdges => {
      return currentEdges.map(edge => {
        const change = changes.find(c => c.id === edge.id);
        if (change) {
          switch (change.type) {
            case 'select':
              return { ...edge, selected: change.selected };
            case 'remove':
              return null;
            default:
              return edge;
          }
        }
        return edge;
      }).filter(Boolean) as Edge[];
    });
  }, []);

  return [edges, setEdges, onEdgesChange];
}

export function useReactFlow() {
  const context = useContext(ReactFlowContext);
  if (!context) {
    // Provide a default implementation if used outside of context
    return {
      getNodes: () => [],
      getEdges: () => [],
      setNodes: () => {},
      setEdges: () => {},
      fitView: () => {},
      zoomIn: () => {},
      zoomOut: () => {},
      zoomTo: () => {},
      getViewport: () => ({ x: 0, y: 0, zoom: 1 }),
      setViewport: () => {},
      viewportInitialized: false,
    };
  }

  return {
    getNodes: () => context.nodes,
    getEdges: () => context.edges,
    setNodes: context.setNodes,
    setEdges: context.setEdges,
    fitView: () => {
      context.setViewport({ x: 0, y: 0, zoom: 1 });
    },
    zoomIn: () => {
      context.setViewport(prev => ({ ...prev, zoom: Math.min(prev.zoom * 1.2, 2) }));
    },
    zoomOut: () => {
      context.setViewport(prev => ({ ...prev, zoom: Math.max(prev.zoom / 1.2, 0.1) }));
    },
    zoomTo: (zoom: number) => {
      context.setViewport(prev => ({ ...prev, zoom }));
    },
    getViewport: () => context.viewport,
    setViewport: context.setViewport,
    viewportInitialized: true,
  };
}

// Utility functions
export function addEdge(edgeParams: Edge | Connection, edges: Edge[]): Edge[] {
  const newEdge: Edge = {
    id: `edge-${edgeParams.source}-${edgeParams.target}`,
    source: edgeParams.source,
    target: edgeParams.target,
    ...('id' in edgeParams ? edgeParams : {}),
  };
  
  return [...edges, newEdge];
}

export function getBezierPath(params: {
  sourceX: number;
  sourceY: number;
  sourcePosition: Position;
  targetX: number;
  targetY: number;
  targetPosition: Position;
}): [string, number, number] {
  const { sourceX, sourceY, targetX, targetY } = params;
  
  // Simple straight line for mock implementation
  const path = `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;
  const labelX = (sourceX + targetX) / 2;
  const labelY = (sourceY + targetY) / 2;
  
  return [path, labelX, labelY];
}

// Components
export const Handle: React.FC<{
  type: 'source' | 'target';
  position: Position;
  id?: string;
  className?: string;
  style?: React.CSSProperties;
}> = ({ type, position, className = '', style = {} }) => {
  const baseStyle: React.CSSProperties = {
    position: 'absolute',
    width: '12px',
    height: '12px',
    borderRadius: '50%',
    border: '2px solid white',
    cursor: 'crosshair',
    zIndex: 10,
    ...style,
  };

  const positionStyle: React.CSSProperties = {};
  switch (position) {
    case Position.Left:
      positionStyle.left = '-6px';
      positionStyle.top = '50%';
      positionStyle.transform = 'translateY(-50%)';
      break;
    case Position.Right:
      positionStyle.right = '-6px';
      positionStyle.top = '50%';
      positionStyle.transform = 'translateY(-50%)';
      break;
    case Position.Top:
      positionStyle.top = '-6px';
      positionStyle.left = '50%';
      positionStyle.transform = 'translateX(-50%)';
      break;
    case Position.Bottom:
      positionStyle.bottom = '-6px';
      positionStyle.left = '50%';
      positionStyle.transform = 'translateX(-50%)';
      break;
  }

  return (
    <div
      className={`react-flow__handle react-flow__handle-${type} ${className}`}
      style={{ ...baseStyle, ...positionStyle }}
    />
  );
};

export const Background: React.FC<{
  variant?: 'dots' | 'lines';
  gap?: number;
  size?: number;
  color?: string;
}> = ({ variant = 'dots' }) => {
  return (
    <div 
      className={`react-flow__background ${variant}`}
      style={{
        position: 'absolute',
        inset: 0,
        zIndex: -1,
      }}
    />
  );
};

export const Controls: React.FC = () => {
  const context = useContext(ReactFlowContext);

  const handleZoomIn = () => {
    if (context) {
      context.setViewport(prev => ({ ...prev, zoom: Math.min(prev.zoom * 1.2, 2) }));
    }
  };

  const handleZoomOut = () => {
    if (context) {
      context.setViewport(prev => ({ ...prev, zoom: Math.max(prev.zoom / 1.2, 0.1) }));
    }
  };

  const handleFitView = () => {
    if (context) {
      context.setViewport({ x: 0, y: 0, zoom: 1 });
    }
  };

  return (
    <div className="react-flow__controls" style={{
      position: 'absolute',
      bottom: '20px',
      left: '20px',
      zIndex: 10,
      display: 'flex',
      flexDirection: 'column',
      gap: '4px',
      background: 'white',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      padding: '4px',
    }}>
      <button
        onClick={handleZoomIn}
        style={{
          border: 'none',
          background: 'transparent',
          padding: '8px',
          cursor: 'pointer',
          borderRadius: '4px',
        }}
        onMouseEnter={(e) => e.currentTarget.style.background = '#f3f4f6'}
        onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
      >
        +
      </button>
      <button
        onClick={handleZoomOut}
        style={{
          border: 'none',
          background: 'transparent',
          padding: '8px',
          cursor: 'pointer',
          borderRadius: '4px',
        }}
        onMouseEnter={(e) => e.currentTarget.style.background = '#f3f4f6'}
        onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
      >
        -
      </button>
      <button
        onClick={handleFitView}
        style={{
          border: 'none',
          background: 'transparent',
          padding: '8px',
          cursor: 'pointer',
          borderRadius: '4px',
        }}
        onMouseEnter={(e) => e.currentTarget.style.background = '#f3f4f6'}
        onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
      >
        ⌂
      </button>
    </div>
  );
};

export const MiniMap: React.FC<{
  nodeColor?: (node: Node) => string;
  zoomable?: boolean;
  pannable?: boolean;
}> = ({ nodeColor }) => {
  const context = useContext(ReactFlowContext);
  const nodes = context?.nodes || [];

  return (
    <div className="react-flow__minimap" style={{
      position: 'absolute',
      bottom: '20px',
      right: '20px',
      width: '200px',
      height: '150px',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      background: 'white',
      zIndex: 10,
      overflow: 'hidden',
    }}>
      <div style={{ padding: '8px', fontSize: '12px', color: '#6b7280' }}>
        Minimap ({nodes.length} nodes)
      </div>
    </div>
  );
};

export const Panel: React.FC<{
  position: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  children: React.ReactNode;
}> = ({ position, children }) => {
  const positionStyles: Record<string, React.CSSProperties> = {
    'top-left': { top: '20px', left: '20px' },
    'top-center': { top: '20px', left: '50%', transform: 'translateX(-50%)' },
    'top-right': { top: '20px', right: '20px' },
    'bottom-left': { bottom: '20px', left: '20px' },
    'bottom-center': { bottom: '20px', left: '50%', transform: 'translateX(-50%)' },
    'bottom-right': { bottom: '20px', right: '20px' },
  };

  return (
    <div 
      className="react-flow__panel"
      style={{
        position: 'absolute',
        zIndex: 10,
        ...positionStyles[position],
      }}
    >
      {children}
    </div>
  );
};

export const EdgeLabelRenderer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <div className="react-flow__edge-label-renderer">{children}</div>;
};

// Main ReactFlow component
interface ReactFlowProps {
  nodes: Node[];
  edges: Edge[];
  onNodesChange?: (changes: any[]) => void;
  onEdgesChange?: (changes: any[]) => void;
  onConnect?: (connection: Connection) => void;
  onNodeClick?: (event: React.MouseEvent, node: Node) => void;
  onEdgeClick?: (event: React.MouseEvent, edge: Edge) => void;
  nodeTypes?: NodeTypes;
  edgeTypes?: EdgeTypes;
  connectionMode?: ConnectionMode;
  fitView?: boolean;
  snapToGrid?: boolean;
  snapGrid?: [number, number];
  defaultEdgeOptions?: Partial<Edge>;
  children?: React.ReactNode;
}

export const ReactFlow: React.FC<ReactFlowProps> = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onConnect,
  onNodeClick,
  onEdgeClick,
  nodeTypes = {},
  edgeTypes = {},
  children,
}) => {
  const context = useContext(ReactFlowContext);
  const canvasRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (context) {
      context.setNodes(nodes);
      context.setEdges(edges);
    }
  }, [nodes, edges, context]);

  const handleNodeClick = (event: React.MouseEvent, node: Node) => {
    if (context) {
      context.setSelectedNode(node);
      context.setSelectedEdge(null);
    }
    onNodeClick?.(event, node);
  };

  const handleCanvasClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget && context) {
      context.setSelectedNode(null);
      context.setSelectedEdge(null);
    }
  };

  return (
    <div 
      ref={canvasRef}
      className="react-flow"
      style={{ 
        position: 'relative', 
        width: '100%', 
        height: '100%',
        overflow: 'hidden',
        background: '#f9fafb',
      }}
      onClick={handleCanvasClick}
    >
      {/* Render edges */}
      <svg 
        style={{ 
          position: 'absolute', 
          inset: 0, 
          width: '100%', 
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1,
        }}
      >
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
          </marker>
        </defs>
        {edges.map(edge => {
          const sourceNode = nodes.find(n => n.id === edge.source);
          const targetNode = nodes.find(n => n.id === edge.target);
          
          if (!sourceNode || !targetNode) return null;
          
          const sourceX = sourceNode.position.x + 128; // node width / 2
          const sourceY = sourceNode.position.y + 32; // node height / 2
          const targetX = targetNode.position.x + 128;
          const targetY = targetNode.position.y + 32;
          
          return (
            <line
              key={edge.id}
              x1={sourceX}
              y1={sourceY}
              x2={targetX}
              y2={targetY}
              stroke={edge.selected ? "#3b82f6" : "#6b7280"}
              strokeWidth={edge.selected ? 3 : 2}
              markerEnd="url(#arrowhead)"
              style={{ pointerEvents: 'stroke', cursor: 'pointer' }}
              onClick={(e) => {
                e.stopPropagation();
                if (context) {
                  context.setSelectedEdge(edge);
                  context.setSelectedNode(null);
                }
                onEdgeClick?.(e as any, edge);
              }}
            />
          );
        })}
      </svg>

      {/* Render nodes */}
      {nodes.map(node => {
        const NodeComponent = nodeTypes[node.type || 'default'];
        
        if (!NodeComponent) {
          return (
            <div
              key={node.id}
              style={{
                position: 'absolute',
                left: node.position.x,
                top: node.position.y,
                padding: '8px 16px',
                background: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '8px',
                cursor: 'pointer',
                zIndex: 2,
              }}
              onClick={(e) => {
                e.stopPropagation();
                handleNodeClick(e, node);
              }}
            >
              {node.data.name || node.id}
            </div>
          );
        }

        return (
          <div
            key={node.id}
            style={{
              position: 'absolute',
              left: node.position.x,
              top: node.position.y,
              zIndex: 2,
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleNodeClick(e, node);
            }}
          >
            <NodeComponent
              id={node.id}
              data={node.data}
              selected={node.selected || (context?.selectedNode?.id === node.id)}
            />
          </div>
        );
      })}

      {children}
    </div>
  );
};
