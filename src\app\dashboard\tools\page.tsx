"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Filter,
  Search,
  Wrench,
  Zap,
  Bot,
  Settings,
  Activity,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import ToolBuilder from "@/components/tool-manager/ToolBuilder";
import { getAPXClient } from "@/lib/apix-client";
import { mockAuthContext } from "@/lib/auth-context";
import { getSessionManager } from "@/lib/session-manager";

export default function ToolsPage() {
  const [showBuilder, setShowBuilder] = useState(false);
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [tools, setTools] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);

  const { user, organization, hasPermission } = mockAuthContext;
  const canCreateTool = hasPermission("tools", "create");
  const canViewTools = hasPermission("tools", "read");

  const loadTools = async () => {
    try {
      const response = await fetch("/api/tools", {
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setTools(result.data.tools);
        } else {
          console.error("Failed to load tools:", result.error);
          // Fallback to mock data
          setTools(mockToolsData);
        }
      } else {
        console.error("Failed to load tools:", response.statusText);
        // Fallback to mock data
        setTools(mockToolsData);
      }
    } catch (error) {
      console.error("Error loading tools:", error);
      // Fallback to mock data
      setTools(mockToolsData);
    }
  };

  useEffect(() => {
    const initializeConnection = async () => {
      try {
        if (user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to tool events
          apixClient.subscribe(
            "tools",
            ["tool_created", "tool_updated", "tool_executed"],
            handleToolEvent,
          );
        }

        // Load tools from API
        await loadTools();
        setLoading(false);
      } catch (error) {
        console.error("Failed to initialize tools page:", error);
        setLoading(false);
      }
    };

    initializeConnection();
  }, [user, organization]);

  const handleToolEvent = (event: any) => {
    switch (event.type) {
      case "tool_created":
        setTools((prev) => [...prev, event.data.tool]);
        break;
      case "tool_updated":
        setTools((prev) =>
          prev.map((tool) =>
            tool.id === event.data.toolId
              ? { ...tool, ...event.data.tool }
              : tool,
          ),
        );
        break;
      case "tool_executed":
        // Update execution count in real-time
        setTools((prev) =>
          prev.map((tool) =>
            tool.id === event.data.toolId
              ? { ...tool, executions: tool.executions + 1 }
              : tool,
          ),
        );
        break;
    }
  };

  const mockToolsData = [
    {
      id: "1",
      name: "SendGrid Email",
      description: "Send transactional emails via SendGrid API",
      category: "email",
      executions: 1245,
      avgResponseTime: "0.5s",
      status: "active",
      cost: "$8.42",
      agentIntegrations: 5,
      workflowIntegrations: 3,
    },
    {
      id: "2",
      name: "Stripe Payments",
      description: "Process payments and manage subscriptions",
      category: "api",
      executions: 892,
      avgResponseTime: "1.2s",
      status: "active",
      cost: "$15.67",
      agentIntegrations: 8,
      workflowIntegrations: 12,
    },
    {
      id: "3",
      name: "PostgreSQL Query",
      description: "Execute database queries and operations",
      category: "database",
      executions: 2156,
      avgResponseTime: "0.3s",
      status: "active",
      cost: "$3.21",
      agentIntegrations: 15,
      workflowIntegrations: 8,
    },
    {
      id: "4",
      name: "PDF Generator",
      description: "Generate PDF documents from templates",
      category: "file",
      executions: 456,
      avgResponseTime: "2.1s",
      status: "active",
      cost: "$12.89",
      agentIntegrations: 3,
      workflowIntegrations: 7,
    },
    {
      id: "5",
      name: "Slack Notifications",
      description: "Send messages to Slack channels",
      category: "communication",
      executions: 789,
      avgResponseTime: "0.7s",
      status: "active",
      cost: "$4.56",
      agentIntegrations: 12,
      workflowIntegrations: 5,
    },
    {
      id: "6",
      name: "Weather API",
      description: "Get current weather and forecasts",
      category: "api",
      executions: 234,
      avgResponseTime: "1.5s",
      status: "draft",
      cost: "$1.23",
      agentIntegrations: 2,
      workflowIntegrations: 1,
    },
  ];

  const handleCreateTool = () => {
    if (!canCreateTool) {
      alert("You do not have permission to create tools.");
      return;
    }
    setSelectedTool(null);
    setShowBuilder(true);
  };

  const handleEditTool = (toolId: string) => {
    setSelectedTool(toolId);
    setShowBuilder(true);
  };

  const handleToolSaved = async (toolData: any) => {
    setShowBuilder(false);
    setSelectedTool(null);

    // Refresh tools list from API
    try {
      const response = await fetch("/api/tools", {
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setTools(result.data.tools);
        }
      }
    } catch (error) {
      console.error("Failed to refresh tools list:", error);
    }
  };

  if (showBuilder) {
    return (
      <ToolBuilder
        toolId={selectedTool || undefined}
        onSave={handleToolSaved}
        onTest={(data) => console.log("Testing tool:", data)}
      />
    );
  }
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tools</h1>
          <p className="text-muted-foreground mt-1">
            Create, manage, and integrate API tools
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            {isConnected ? "Live" : "Offline"}
          </Badge>
          <Button
            className="flex items-center gap-2"
            onClick={handleCreateTool}
            disabled={!canCreateTool}
          >
            <Plus className="h-4 w-4" />
            Create Tool
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="all">All Tools</TabsTrigger>
            <TabsTrigger value="api">API Tools</TabsTrigger>
            <TabsTrigger value="database">Database</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="file">File Processing</TabsTrigger>
          </TabsList>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search tools..."
                className="pl-8 w-[250px]"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="mt-0">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Tools</CardTitle>
                <CardDescription>Across all categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{tools.length}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Active Tools</CardTitle>
                <CardDescription>Currently in use</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {tools.filter((t) => t.status === "active").length}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Executions</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {tools
                    .reduce((sum, t) => sum + t.executions, 0)
                    .toLocaleString()}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Agent Integrations</CardTitle>
                <CardDescription>Tools connected to agents</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {tools.reduce((sum, t) => sum + t.agentIntegrations, 0)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tool Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {tools.map((tool) => (
              <Card
                key={tool.id}
                className="hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => handleEditTool(tool.id)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Wrench className="h-5 w-5" />
                      {tool.name}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          tool.status === "active" ? "default" : "secondary"
                        }
                        className="text-xs"
                      >
                        {tool.status}
                      </Badge>
                      {tool.agentIntegrations > 0 && (
                        <Badge
                          variant="outline"
                          className="text-xs flex items-center gap-1"
                        >
                          <Bot className="h-3 w-3" />
                          {tool.agentIntegrations}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <CardDescription>{tool.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Executions</div>
                      <div className="font-medium">
                        {tool.executions.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Avg. Time</div>
                      <div className="font-medium">{tool.avgResponseTime}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Agent Links</div>
                      <div className="font-medium flex items-center gap-1">
                        <Bot className="h-3 w-3" />
                        {tool.agentIntegrations}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Cost</div>
                      <div className="font-medium">{tool.cost}</div>
                    </div>
                  </div>

                  {/* Integration Status */}
                  <div className="mt-4 pt-4 border-t">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-muted-foreground">
                        Integration Status:
                      </span>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {tool.agentIntegrations} Agents
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {tool.workflowIntegrations} Workflows
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="api">
          <div className="text-center py-8 text-muted-foreground">
            API tools will be displayed here.
          </div>
        </TabsContent>

        <TabsContent value="database">
          <div className="text-center py-8 text-muted-foreground">
            Database tools will be displayed here.
          </div>
        </TabsContent>

        <TabsContent value="email">
          <div className="text-center py-8 text-muted-foreground">
            Email tools will be displayed here.
          </div>
        </TabsContent>

        <TabsContent value="file">
          <div className="text-center py-8 text-muted-foreground">
            File processing tools will be displayed here.
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
