// Production AI Provider Integration
// Multi-provider support with intelligent routing, failover, and cost optimization

import OpenA<PERSON> from "openai";
import Anthropic from "@anthropic-ai/sdk";
import { GoogleGenerativeAI } from "@google/generative-ai";

interface ProviderConfig {
  apiKey: string;
  baseURL?: string;
  timeout: number;
  maxRetries: number;
  rateLimits: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

interface AIRequest {
  provider: string;
  model: string;
  messages: Array<{
    role: "system" | "user" | "assistant";
    content: string;
  }>;
  temperature: number;
  maxTokens: number;
  organizationId: string;
  userId: string;
}

interface AIResponse {
  content: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost: number;
  latency: number;
  model: string;
  provider: string;
  finishReason: string;
}

interface ProviderMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgLatency: number;
  totalCost: number;
  lastUsed: Date;
  errorRate: number;
  availability: number;
}

class AIProviderManager {
  private static instance: AIProviderManager;
  private providers: Map<string, any> = new Map();
  private configs: Map<string, ProviderConfig> = new Map();
  private metrics: Map<string, ProviderMetrics> = new Map();
  private rateLimiters: Map<string, { count: number; resetTime: number }> =
    new Map();
  private circuitBreakers: Map<
    string,
    { isOpen: boolean; failures: number; lastFailure: Date }
  > = new Map();

  private constructor() {
    this.initializeProviders();
  }

  public static getInstance(): AIProviderManager {
    if (!AIProviderManager.instance) {
      AIProviderManager.instance = new AIProviderManager();
    }
    return AIProviderManager.instance;
  }

  private initializeProviders(): void {
    // OpenAI Configuration
    if (process.env.OPENAI_API_KEY) {
      const openaiConfig: ProviderConfig = {
        apiKey: process.env.OPENAI_API_KEY,
        timeout: 60000,
        maxRetries: 3,
        rateLimits: {
          requestsPerMinute: 3500,
          tokensPerMinute: 90000,
        },
      };

      const openai = new OpenAI({
        apiKey: openaiConfig.apiKey,
        timeout: openaiConfig.timeout,
        maxRetries: openaiConfig.maxRetries,
      });

      this.providers.set("openai", openai);
      this.configs.set("openai", openaiConfig);
      this.initializeMetrics("openai");
    }

    // Anthropic Configuration
    if (process.env.ANTHROPIC_API_KEY) {
      const anthropicConfig: ProviderConfig = {
        apiKey: process.env.ANTHROPIC_API_KEY,
        timeout: 60000,
        maxRetries: 3,
        rateLimits: {
          requestsPerMinute: 1000,
          tokensPerMinute: 40000,
        },
      };

      const anthropic = new Anthropic({
        apiKey: anthropicConfig.apiKey,
        timeout: anthropicConfig.timeout,
        maxRetries: anthropicConfig.maxRetries,
      });

      this.providers.set("anthropic", anthropic);
      this.configs.set("anthropic", anthropicConfig);
      this.initializeMetrics("anthropic");
    }

    // Google AI Configuration
    if (process.env.GOOGLE_AI_API_KEY) {
      const googleConfig: ProviderConfig = {
        apiKey: process.env.GOOGLE_AI_API_KEY,
        timeout: 60000,
        maxRetries: 3,
        rateLimits: {
          requestsPerMinute: 1500,
          tokensPerMinute: 32000,
        },
      };

      const google = new GoogleGenerativeAI(googleConfig.apiKey);

      this.providers.set("google", google);
      this.configs.set("google", googleConfig);
      this.initializeMetrics("google");
    }

    // Groq Configuration
    if (process.env.GROQ_API_KEY) {
      const groqConfig: ProviderConfig = {
        apiKey: process.env.GROQ_API_KEY,
        baseURL: "https://api.groq.com/openai/v1",
        timeout: 30000,
        maxRetries: 3,
        rateLimits: {
          requestsPerMinute: 30,
          tokensPerMinute: 6000,
        },
      };

      const groq = new OpenAI({
        apiKey: groqConfig.apiKey,
        baseURL: groqConfig.baseURL,
        timeout: groqConfig.timeout,
        maxRetries: groqConfig.maxRetries,
      });

      this.providers.set("groq", groq);
      this.configs.set("groq", groqConfig);
      this.initializeMetrics("groq");
    }

    // Mistral Configuration
    if (process.env.MISTRAL_API_KEY) {
      const mistralConfig: ProviderConfig = {
        apiKey: process.env.MISTRAL_API_KEY,
        baseURL: "https://api.mistral.ai/v1",
        timeout: 60000,
        maxRetries: 3,
        rateLimits: {
          requestsPerMinute: 1000,
          tokensPerMinute: 1000000,
        },
      };

      const mistral = new OpenAI({
        apiKey: mistralConfig.apiKey,
        baseURL: mistralConfig.baseURL,
        timeout: mistralConfig.timeout,
        maxRetries: mistralConfig.maxRetries,
      });

      this.providers.set("mistral", mistral);
      this.configs.set("mistral", mistralConfig);
      this.initializeMetrics("mistral");
    }
  }

  private initializeMetrics(provider: string): void {
    this.metrics.set(provider, {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      avgLatency: 0,
      totalCost: 0,
      lastUsed: new Date(),
      errorRate: 0,
      availability: 1.0,
    });

    this.circuitBreakers.set(provider, {
      isOpen: false,
      failures: 0,
      lastFailure: new Date(0),
    });
  }

  private checkRateLimit(provider: string): boolean {
    const config = this.configs.get(provider);
    if (!config) return false;

    const now = Date.now();
    const windowStart = Math.floor(now / 60000) * 60000; // 1-minute window
    const key = `${provider}_${windowStart}`;

    const current = this.rateLimiters.get(key) || {
      count: 0,
      resetTime: windowStart + 60000,
    };

    if (current.count >= config.rateLimits.requestsPerMinute) {
      return false;
    }

    this.rateLimiters.set(key, { ...current, count: current.count + 1 });
    return true;
  }

  private checkCircuitBreaker(provider: string): boolean {
    const breaker = this.circuitBreakers.get(provider);
    if (!breaker) return true;

    if (breaker.isOpen) {
      const timeSinceLastFailure = Date.now() - breaker.lastFailure.getTime();
      if (timeSinceLastFailure > 60000) {
        // 1 minute timeout
        breaker.isOpen = false;
        breaker.failures = 0;
      } else {
        return false;
      }
    }

    return true;
  }

  private recordFailure(provider: string): void {
    const breaker = this.circuitBreakers.get(provider);
    if (breaker) {
      breaker.failures++;
      breaker.lastFailure = new Date();

      if (breaker.failures >= 5) {
        breaker.isOpen = true;
        console.warn(`Circuit breaker opened for provider: ${provider}`);
      }
    }

    const metrics = this.metrics.get(provider);
    if (metrics) {
      metrics.failedRequests++;
      metrics.errorRate = metrics.failedRequests / metrics.totalRequests;
      metrics.availability = metrics.successfulRequests / metrics.totalRequests;
    }
  }

  private recordSuccess(provider: string, latency: number, cost: number): void {
    const breaker = this.circuitBreakers.get(provider);
    if (breaker) {
      breaker.failures = Math.max(0, breaker.failures - 1);
    }

    const metrics = this.metrics.get(provider);
    if (metrics) {
      metrics.successfulRequests++;
      metrics.avgLatency =
        (metrics.avgLatency * (metrics.successfulRequests - 1) + latency) /
        metrics.successfulRequests;
      metrics.totalCost += cost;
      metrics.lastUsed = new Date();
      metrics.errorRate = metrics.failedRequests / metrics.totalRequests;
      metrics.availability = metrics.successfulRequests / metrics.totalRequests;
    }
  }

  private calculateCost(
    provider: string,
    model: string,
    usage: { promptTokens: number; completionTokens: number },
  ): number {
    // Cost calculation based on provider and model
    const costMap: Record<
      string,
      Record<string, { input: number; output: number }>
    > = {
      openai: {
        "gpt-4o": { input: 0.005, output: 0.015 },
        "gpt-4-turbo": { input: 0.01, output: 0.03 },
        "gpt-3.5-turbo": { input: 0.0005, output: 0.0015 },
      },
      anthropic: {
        "claude-3-opus": { input: 0.015, output: 0.075 },
        "claude-3-sonnet": { input: 0.003, output: 0.015 },
        "claude-3-haiku": { input: 0.00025, output: 0.00125 },
      },
      google: {
        "gemini-pro": { input: 0.0005, output: 0.0015 },
        "gemini-ultra": { input: 0.01, output: 0.03 },
      },
      groq: {
        "llama3-70b": { input: 0.00059, output: 0.00079 },
        "llama3-8b": { input: 0.00005, output: 0.00008 },
        "mixtral-8x7b": { input: 0.00024, output: 0.00024 },
      },
      mistral: {
        "mistral-large": { input: 0.008, output: 0.024 },
        "mistral-medium": { input: 0.0027, output: 0.0081 },
        "mistral-small": { input: 0.002, output: 0.006 },
      },
    };

    const pricing = costMap[provider]?.[model];
    if (!pricing) return 0;

    return (
      (usage.promptTokens / 1000) * pricing.input +
      (usage.completionTokens / 1000) * pricing.output
    );
  }

  async generateResponse(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    // Check rate limits
    if (!this.checkRateLimit(request.provider)) {
      throw new Error(`Rate limit exceeded for provider: ${request.provider}`);
    }

    // Check circuit breaker
    if (!this.checkCircuitBreaker(request.provider)) {
      throw new Error(
        `Circuit breaker is open for provider: ${request.provider}`,
      );
    }

    const provider = this.providers.get(request.provider);
    if (!provider) {
      throw new Error(`Provider not configured: ${request.provider}`);
    }

    // Update metrics
    const metrics = this.metrics.get(request.provider);
    if (metrics) {
      metrics.totalRequests++;
    }

    try {
      let response: AIResponse;

      switch (request.provider) {
        case "openai":
        case "groq":
        case "mistral":
          response = await this.handleOpenAICompatible(provider, request);
          break;
        case "anthropic":
          response = await this.handleAnthropic(provider, request);
          break;
        case "google":
          response = await this.handleGoogle(provider, request);
          break;
        default:
          throw new Error(`Unsupported provider: ${request.provider}`);
      }

      const latency = Date.now() - startTime;
      response.latency = latency;
      response.cost = this.calculateCost(
        request.provider,
        request.model,
        response.usage,
      );

      this.recordSuccess(request.provider, latency, response.cost);

      return response;
    } catch (error) {
      this.recordFailure(request.provider);
      console.error(`AI Provider Error (${request.provider}):`, error);
      throw error;
    }
  }

  private async handleOpenAICompatible(
    provider: any,
    request: AIRequest,
  ): Promise<AIResponse> {
    const completion = await provider.chat.completions.create({
      model: request.model,
      messages: request.messages,
      temperature: request.temperature,
      max_tokens: request.maxTokens,
      user: `${request.organizationId}:${request.userId}`,
    });

    return {
      content: completion.choices[0]?.message?.content || "",
      usage: {
        promptTokens: completion.usage?.prompt_tokens || 0,
        completionTokens: completion.usage?.completion_tokens || 0,
        totalTokens: completion.usage?.total_tokens || 0,
      },
      cost: 0, // Will be calculated later
      latency: 0, // Will be set later
      model: completion.model,
      provider: request.provider,
      finishReason: completion.choices[0]?.finish_reason || "unknown",
    };
  }

  private async handleAnthropic(
    provider: any,
    request: AIRequest,
  ): Promise<AIResponse> {
    const systemMessage = request.messages.find((m) => m.role === "system");
    const userMessages = request.messages.filter((m) => m.role !== "system");

    const completion = await provider.messages.create({
      model: request.model,
      max_tokens: request.maxTokens,
      temperature: request.temperature,
      system: systemMessage?.content || "",
      messages: userMessages.map((m) => ({
        role: m.role === "assistant" ? "assistant" : "user",
        content: m.content,
      })),
      metadata: {
        user_id: `${request.organizationId}:${request.userId}`,
      },
    });

    return {
      content: completion.content[0]?.text || "",
      usage: {
        promptTokens: completion.usage.input_tokens,
        completionTokens: completion.usage.output_tokens,
        totalTokens:
          completion.usage.input_tokens + completion.usage.output_tokens,
      },
      cost: 0, // Will be calculated later
      latency: 0, // Will be set later
      model: completion.model,
      provider: request.provider,
      finishReason: completion.stop_reason || "unknown",
    };
  }

  private async handleGoogle(
    provider: any,
    request: AIRequest,
  ): Promise<AIResponse> {
    const model = provider.getGenerativeModel({ model: request.model });

    const systemMessage = request.messages.find((m) => m.role === "system");
    const userMessages = request.messages.filter((m) => m.role !== "system");

    const prompt = [
      systemMessage?.content || "",
      ...userMessages.map((m) => `${m.role}: ${m.content}`),
    ].join("\n\n");

    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: request.temperature,
        maxOutputTokens: request.maxTokens,
      },
    });

    const response = result.response;
    const text = response.text();

    return {
      content: text,
      usage: {
        promptTokens: response.usageMetadata?.promptTokenCount || 0,
        completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: response.usageMetadata?.totalTokenCount || 0,
      },
      cost: 0, // Will be calculated later
      latency: 0, // Will be set later
      model: request.model,
      provider: request.provider,
      finishReason: response.candidates?.[0]?.finishReason || "unknown",
    };
  }

  // Provider selection with intelligent routing
  selectOptimalProvider(requirements: {
    preferredProvider?: string;
    model?: string;
    maxCost?: number;
    maxLatency?: number;
    minAvailability?: number;
  }): string | null {
    const availableProviders = Array.from(this.providers.keys()).filter(
      (provider) => {
        const metrics = this.metrics.get(provider);
        const breaker = this.circuitBreakers.get(provider);

        if (!metrics || !breaker) return false;
        if (breaker.isOpen) return false;
        if (
          requirements.minAvailability &&
          metrics.availability < requirements.minAvailability
        )
          return false;
        if (
          requirements.maxLatency &&
          metrics.avgLatency > requirements.maxLatency
        )
          return false;

        return true;
      },
    );

    if (availableProviders.length === 0) return null;

    // Prefer specified provider if available and meets requirements
    if (
      requirements.preferredProvider &&
      availableProviders.includes(requirements.preferredProvider)
    ) {
      return requirements.preferredProvider;
    }

    // Select based on performance score
    const scored = availableProviders.map((provider) => {
      const metrics = this.metrics.get(provider)!;
      const score =
        metrics.availability * 0.4 +
        (1 - metrics.errorRate) * 0.3 +
        (1 / (metrics.avgLatency + 1)) * 0.2 +
        (1 / (metrics.totalCost + 1)) * 0.1;
      return { provider, score };
    });

    scored.sort((a, b) => b.score - a.score);
    return scored[0]?.provider || null;
  }

  getProviderMetrics(
    provider?: string,
  ): Record<string, ProviderMetrics> | ProviderMetrics | null {
    if (provider) {
      return this.metrics.get(provider) || null;
    }
    return Object.fromEntries(this.metrics.entries());
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  isProviderAvailable(provider: string): boolean {
    const breaker = this.circuitBreakers.get(provider);
    return this.providers.has(provider) && (!breaker || !breaker.isOpen);
  }
}

// Export singleton instance
export const aiProviders = AIProviderManager.getInstance();
export type { AIRequest, AIResponse, ProviderMetrics };
