"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Switch } from "../ui/switch";
import { Separator } from "../ui/separator";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import {
  Code,
  Copy,
  Download,
  Eye,
  Globe,
  Palette,
  Settings,
  Share,
  Smartphone,
  Monitor,
  Tablet,
  Wifi,
  WifiOff,
  Shield,
  Activity,
  Zap,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from "../ui/tooltip";
import { getAPXClient } from "../../lib/apix-client";
import { mockAuthContext } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";
import { createWidgetCreatedEvent } from "../../lib/apix-events";

interface WidgetConfig {
  id: string;
  name: string;
  description: string;
  type: "agent" | "tool" | "hybrid";
  sourceId: string;
  sourceName: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
    backgroundColor: string;
    textColor: string;
    borderRadius: number;
    fontFamily: string;
  };
  branding: {
    showLogo: boolean;
    logoUrl?: string;
    showPoweredBy: boolean;
    customFooter?: string;
  };
  behavior: {
    autoOpen: boolean;
    position: "bottom-right" | "bottom-left" | "center" | "fullscreen";
    size: "small" | "medium" | "large" | "custom";
    customWidth?: number;
    customHeight?: number;
  };
  features: {
    voiceInterface: boolean;
    multiLanguage: boolean;
    offlineMode: boolean;
    analytics: boolean;
    customCSS?: string;
    customJS?: string;
  };
  security: {
    allowedDomains: string[];
    requireAuth: boolean;
    rateLimiting: {
      enabled: boolean;
      requestsPerMinute: number;
    };
  };
}

interface WidgetGeneratorProps {
  sourceType: "agent" | "tool" | "hybrid";
  sourceId: string;
  sourceName: string;
  onGenerate?: (config: WidgetConfig) => void;
}

const WidgetGenerator: React.FC<WidgetGeneratorProps> = ({
  sourceType,
  sourceId,
  sourceName,
  onGenerate = () => {},
}) => {
  const defaultConfig: WidgetConfig = {
    id: `widget-${Date.now()}`,
    name: `${sourceName} Widget`,
    description: `Embeddable widget for ${sourceName}`,
    type: sourceType,
    sourceId,
    sourceName,
    theme: {
      primaryColor: "#3b82f6",
      secondaryColor: "#1e40af",
      backgroundColor: "#ffffff",
      textColor: "#1f2937",
      borderRadius: 8,
      fontFamily: "Inter, sans-serif",
    },
    branding: {
      showLogo: true,
      showPoweredBy: true,
    },
    behavior: {
      autoOpen: false,
      position: "bottom-right",
      size: "medium",
    },
    features: {
      voiceInterface: false,
      multiLanguage: false,
      offlineMode: false,
      analytics: true,
    },
    security: {
      allowedDomains: [],
      requireAuth: false,
      rateLimiting: {
        enabled: true,
        requestsPerMinute: 60,
      },
    },
  };

  const [config, setConfig] = useState<WidgetConfig>(defaultConfig);
  const [activeTab, setActiveTab] = useState("design");
  const [previewDevice, setPreviewDevice] = useState<
    "desktop" | "tablet" | "mobile"
  >("desktop");
  const [embedFormat, setEmbedFormat] = useState<
    "javascript" | "iframe" | "wordpress" | "shopify"
  >("javascript");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCode, setGeneratedCode] = useState("");

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canCreateWidget = checkPermission("widgets", "create");
  const canCustomizeWidget = checkPermission("widgets", "customize");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        if (user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to widget events
          apixClient.subscribe(
            "widgets",
            ["created", "embedded", "executed"],
            handleRealtimeEvent,
          );

          // Create session for widget generator
          const newSessionId = await sessionManager.createSession(
            "widgets",
            "user",
            { sourceType, sourceId, generatorState: config },
            {
              tags: ["widget-generator"],
              conversationId: `generator-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);
        }
      } catch (error) {
        console.error("Failed to initialize APIX:", error);
        setIsConnected(false);
      }
    };

    initializeAPX();

    return () => {
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
    };
  }, [user, organization]);

  const handleRealtimeEvent = useCallback(
    (event: any) => {
      switch (event.type) {
        case "created":
          if (event.data.sourceId === sourceId) {
            console.log("Widget created:", event.data);
          }
          break;
        case "embedded":
          console.log("Widget embedded:", event.data);
          break;
        case "executed":
          console.log("Widget executed:", event.data);
          break;
      }
    },
    [sourceId],
  );

  const updateConfig = (path: string, value: any) => {
    setConfig((prev) => {
      const keys = path.split(".");
      const newConfig = { ...prev };
      let current: any = newConfig;

      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;

      // Update session with new config
      if (sessionId) {
        sessionManager.updateSession(sessionId, { generatorState: newConfig });
      }

      return newConfig;
    });
  };

  const generateEmbedCode = (format: string) => {
    const widgetUrl = `https://widgets.synapseai.com/${config.id}`;
    const configJson = JSON.stringify(config, null, 2);

    switch (format) {
      case "javascript":
        return `<!-- SynapseAI Widget -->
<script>
  (function() {
    var script = document.createElement('script');
    script.src = 'https://widgets.synapseai.com/embed.js';
    script.async = true;
    script.onload = function() {
      SynapseAI.init({
        widgetId: '${config.id}',
        config: ${configJson}
      });
    };
    document.head.appendChild(script);
  })();
</script>`;

      case "iframe":
        return `<iframe 
  src="${widgetUrl}" 
  width="${config.behavior.size === "custom" ? config.behavior.customWidth : "400"}" 
  height="${config.behavior.size === "custom" ? config.behavior.customHeight : "600"}"
  frameborder="0"
  allow="microphone; camera"
  title="${config.name}">
</iframe>`;

      case "wordpress":
        return `[synapseai_widget id="${config.id}" width="400" height="600"]`;

      case "shopify":
        return `{% comment %} SynapseAI Widget {% endcomment %}
<div id="synapseai-widget-${config.id}"></div>
<script>
  window.SynapseAIConfig = ${configJson};
</script>
<script src="https://widgets.synapseai.com/shopify.js" async></script>`;

      default:
        return "";
    }
  };

  const handleGenerate = async () => {
    if (!canCreateWidget) {
      alert("You do not have permission to create widgets.");
      return;
    }

    setIsGenerating(true);
    try {
      // Generate embed code
      const code = generateEmbedCode(embedFormat);
      setGeneratedCode(code);

      // Broadcast widget creation event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );

        const widgetEvent = createWidgetCreatedEvent(organization.id, user.id, {
          widgetId: config.id,
          name: config.name,
          type: config.type,
          sourceId: config.sourceId,
          theme: JSON.stringify(config.theme),
          embedFormat,
          customization: config,
          createdBy: user.id,
        });

        apixClient.sendEvent(widgetEvent);
      }

      // Update session with generated widget
      if (sessionId) {
        await sessionManager.updateSession(sessionId, {
          generatedWidget: config,
          embedCode: code,
          generatedAt: new Date().toISOString(),
        });
      }

      onGenerate(config);
    } catch (error) {
      console.error("Error generating widget:", error);
      alert("Error generating widget. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getPreviewDimensions = () => {
    switch (previewDevice) {
      case "mobile":
        return { width: "375px", height: "667px" };
      case "tablet":
        return { width: "768px", height: "1024px" };
      default:
        return { width: "100%", height: "600px" };
    }
  };

  return (
    <div className="bg-background w-full h-full flex flex-col">
      <div className="flex justify-between items-center p-4 border-b">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">Widget Generator</h1>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    {isConnected ? (
                      <Wifi className="h-4 w-4 text-green-500" />
                    ) : (
                      <WifiOff className="h-4 w-4 text-red-500" />
                    )}
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {isConnected
                        ? "Connected to APIX"
                        : "Disconnected from APIX"}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role}
              </Badge>
              <Badge variant="secondary">{organization?.name}</Badge>
            </div>
          </div>
          <p className="text-muted-foreground mt-1">
            Create embeddable widget for {sourceName} ({sourceType})
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => {}}>
            <Eye className="mr-2 h-4 w-4" /> Preview
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !canCreateWidget}
          >
            {isGenerating ? (
              <>
                <span className="animate-spin mr-2">⏳</span> Generating...
              </>
            ) : (
              <>
                <Zap className="mr-2 h-4 w-4" /> Generate Widget
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Configuration Panel */}
        <div className="w-1/2 p-4 overflow-y-auto border-r">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="mb-4">
              <TabsTrigger value="design">
                <Palette className="mr-2 h-4 w-4" /> Design
              </TabsTrigger>
              <TabsTrigger value="behavior">
                <Settings className="mr-2 h-4 w-4" /> Behavior
              </TabsTrigger>
              <TabsTrigger value="features">
                <Activity className="mr-2 h-4 w-4" /> Features
              </TabsTrigger>
              <TabsTrigger value="security">
                <Shield className="mr-2 h-4 w-4" /> Security
              </TabsTrigger>
            </TabsList>

            <TabsContent value="design" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Theme & Branding</CardTitle>
                  <CardDescription>
                    Customize the visual appearance of your widget
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Primary Color</Label>
                      <div className="flex gap-2">
                        <Input
                          type="color"
                          value={config.theme.primaryColor}
                          onChange={(e) =>
                            updateConfig("theme.primaryColor", e.target.value)
                          }
                          className="w-16 h-9 p-1"
                        />
                        <Input
                          value={config.theme.primaryColor}
                          onChange={(e) =>
                            updateConfig("theme.primaryColor", e.target.value)
                          }
                          placeholder="#3b82f6"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Secondary Color</Label>
                      <div className="flex gap-2">
                        <Input
                          type="color"
                          value={config.theme.secondaryColor}
                          onChange={(e) =>
                            updateConfig("theme.secondaryColor", e.target.value)
                          }
                          className="w-16 h-9 p-1"
                        />
                        <Input
                          value={config.theme.secondaryColor}
                          onChange={(e) =>
                            updateConfig("theme.secondaryColor", e.target.value)
                          }
                          placeholder="#1e40af"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Font Family</Label>
                    <Select
                      value={config.theme.fontFamily}
                      onValueChange={(value) =>
                        updateConfig("theme.fontFamily", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Inter, sans-serif">Inter</SelectItem>
                        <SelectItem value="Roboto, sans-serif">
                          Roboto
                        </SelectItem>
                        <SelectItem value="Open Sans, sans-serif">
                          Open Sans
                        </SelectItem>
                        <SelectItem value="Poppins, sans-serif">
                          Poppins
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Border Radius: {config.theme.borderRadius}px</Label>
                    <Input
                      type="range"
                      min="0"
                      max="20"
                      value={config.theme.borderRadius}
                      onChange={(e) =>
                        updateConfig(
                          "theme.borderRadius",
                          parseInt(e.target.value),
                        )
                      }
                    />
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={config.branding.showLogo}
                        onCheckedChange={(checked) =>
                          updateConfig("branding.showLogo", checked)
                        }
                      />
                      <Label>Show Logo</Label>
                    </div>

                    {config.branding.showLogo && (
                      <div className="space-y-2">
                        <Label>Logo URL</Label>
                        <Input
                          value={config.branding.logoUrl || ""}
                          onChange={(e) =>
                            updateConfig("branding.logoUrl", e.target.value)
                          }
                          placeholder="https://example.com/logo.png"
                        />
                      </div>
                    )}

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={config.branding.showPoweredBy}
                        onCheckedChange={(checked) =>
                          updateConfig("branding.showPoweredBy", checked)
                        }
                      />
                      <Label>Show "Powered by SynapseAI"</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="behavior" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Widget Behavior</CardTitle>
                  <CardDescription>
                    Configure how the widget appears and behaves
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Position</Label>
                    <Select
                      value={config.behavior.position}
                      onValueChange={(value: any) =>
                        updateConfig("behavior.position", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bottom-right">
                          Bottom Right
                        </SelectItem>
                        <SelectItem value="bottom-left">Bottom Left</SelectItem>
                        <SelectItem value="center">Center</SelectItem>
                        <SelectItem value="fullscreen">Fullscreen</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Size</Label>
                    <Select
                      value={config.behavior.size}
                      onValueChange={(value: any) =>
                        updateConfig("behavior.size", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Small (300x400)</SelectItem>
                        <SelectItem value="medium">Medium (400x600)</SelectItem>
                        <SelectItem value="large">Large (600x800)</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {config.behavior.size === "custom" && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Width (px)</Label>
                        <Input
                          type="number"
                          value={config.behavior.customWidth || 400}
                          onChange={(e) =>
                            updateConfig(
                              "behavior.customWidth",
                              parseInt(e.target.value),
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Height (px)</Label>
                        <Input
                          type="number"
                          value={config.behavior.customHeight || 600}
                          onChange={(e) =>
                            updateConfig(
                              "behavior.customHeight",
                              parseInt(e.target.value),
                            )
                          }
                        />
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.behavior.autoOpen}
                      onCheckedChange={(checked) =>
                        updateConfig("behavior.autoOpen", checked)
                      }
                    />
                    <Label>Auto-open on page load</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="features" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Features</CardTitle>
                  <CardDescription>
                    Enable additional capabilities for your widget
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={config.features.voiceInterface}
                        onCheckedChange={(checked) =>
                          updateConfig("features.voiceInterface", checked)
                        }
                        disabled={!canCustomizeWidget}
                      />
                      <Label>Voice Interface</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={config.features.multiLanguage}
                        onCheckedChange={(checked) =>
                          updateConfig("features.multiLanguage", checked)
                        }
                        disabled={!canCustomizeWidget}
                      />
                      <Label>Multi-language Support</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={config.features.offlineMode}
                        onCheckedChange={(checked) =>
                          updateConfig("features.offlineMode", checked)
                        }
                        disabled={!canCustomizeWidget}
                      />
                      <Label>Offline Mode</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={config.features.analytics}
                        onCheckedChange={(checked) =>
                          updateConfig("features.analytics", checked)
                        }
                      />
                      <Label>Analytics Tracking</Label>
                    </div>
                  </div>

                  <Separator />

                  {canCustomizeWidget && (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>Custom CSS</Label>
                        <Textarea
                          value={config.features.customCSS || ""}
                          onChange={(e) =>
                            updateConfig("features.customCSS", e.target.value)
                          }
                          placeholder="/* Custom CSS styles */"
                          rows={4}
                          className="font-mono"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>Custom JavaScript</Label>
                        <Textarea
                          value={config.features.customJS || ""}
                          onChange={(e) =>
                            updateConfig("features.customJS", e.target.value)
                          }
                          placeholder="// Custom JavaScript code"
                          rows={4}
                          className="font-mono"
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>
                    Configure security and access controls
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Allowed Domains (one per line)</Label>
                    <Textarea
                      value={config.security.allowedDomains.join("\n")}
                      onChange={(e) =>
                        updateConfig(
                          "security.allowedDomains",
                          e.target.value.split("\n").filter(Boolean),
                        )
                      }
                      placeholder="example.com\napp.example.com"
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={config.security.requireAuth}
                      onCheckedChange={(checked) =>
                        updateConfig("security.requireAuth", checked)
                      }
                    />
                    <Label>Require Authentication</Label>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={config.security.rateLimiting.enabled}
                        onCheckedChange={(checked) =>
                          updateConfig("security.rateLimiting.enabled", checked)
                        }
                      />
                      <Label>Enable Rate Limiting</Label>
                    </div>

                    {config.security.rateLimiting.enabled && (
                      <div className="space-y-2">
                        <Label>Requests per Minute</Label>
                        <Input
                          type="number"
                          value={config.security.rateLimiting.requestsPerMinute}
                          onChange={(e) =>
                            updateConfig(
                              "security.rateLimiting.requestsPerMinute",
                              parseInt(e.target.value),
                            )
                          }
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Preview & Code Panel */}
        <div className="w-1/2 p-4 overflow-y-auto">
          <div className="space-y-4">
            {/* Preview Controls */}
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Preview</h3>
              <div className="flex gap-2">
                <Button
                  variant={previewDevice === "desktop" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPreviewDevice("desktop")}
                >
                  <Monitor className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewDevice === "tablet" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPreviewDevice("tablet")}
                >
                  <Tablet className="h-4 w-4" />
                </Button>
                <Button
                  variant={previewDevice === "mobile" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPreviewDevice("mobile")}
                >
                  <Smartphone className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Preview Frame */}
            <div className="border rounded-lg p-4 bg-gray-50">
              <div
                className="mx-auto border rounded-lg bg-white shadow-sm"
                style={getPreviewDimensions()}
              >
                <div
                  className="h-full rounded-lg flex flex-col"
                  style={{
                    backgroundColor: config.theme.backgroundColor,
                    color: config.theme.textColor,
                    fontFamily: config.theme.fontFamily,
                    borderRadius: `${config.theme.borderRadius}px`,
                  }}
                >
                  {/* Widget Header */}
                  <div
                    className="p-4 border-b flex items-center justify-between"
                    style={{ borderColor: config.theme.primaryColor + "20" }}
                  >
                    <div className="flex items-center gap-2">
                      {config.branding.showLogo && (
                        <div className="w-6 h-6 bg-gray-200 rounded"></div>
                      )}
                      <span className="font-medium">{config.name}</span>
                    </div>
                    <Badge
                      style={{
                        backgroundColor: config.theme.primaryColor,
                        color: "white",
                      }}
                    >
                      {config.type}
                    </Badge>
                  </div>

                  {/* Widget Content */}
                  <div className="flex-1 p-4 flex items-center justify-center">
                    <div className="text-center space-y-2">
                      <div className="text-lg font-medium">Widget Preview</div>
                      <div className="text-sm text-muted-foreground">
                        {config.description}
                      </div>
                      <Button
                        style={{
                          backgroundColor: config.theme.primaryColor,
                          color: "white",
                        }}
                      >
                        Start Conversation
                      </Button>
                    </div>
                  </div>

                  {/* Widget Footer */}
                  {config.branding.showPoweredBy && (
                    <div className="p-2 text-xs text-center text-muted-foreground border-t">
                      Powered by SynapseAI
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Embed Code */}
            {generatedCode && (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Embed Code</CardTitle>
                    <div className="flex gap-2">
                      <Select
                        value={embedFormat}
                        onValueChange={(value) => setEmbedFormat(value as "javascript" | "iframe" | "wordpress" | "shopify")}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="javascript">JavaScript</SelectItem>
                          <SelectItem value="iframe">iFrame</SelectItem>
                          <SelectItem value="wordpress">WordPress</SelectItem>
                          <SelectItem value="shopify">Shopify</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(generatedCode)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-32">
                    <pre className="text-sm font-mono bg-muted p-3 rounded">
                      {generateEmbedCode(embedFormat)}
                    </pre>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WidgetGenerator;
