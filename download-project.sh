#!/bin/bash

# SynapseAI Project Download Script
# This script creates a downloadable archive of the entire SynapseAI project

echo "🚀 Creating SynapseAI project download package..."

# Create a temporary directory for the package
TEMP_DIR="synapseai-download-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$TEMP_DIR"

echo "📦 Copying project files..."

# Copy all project files except node_modules, .git, and other build artifacts
rsync -av --progress \
  --exclude='node_modules' \
  --exclude='.git' \
  --exclude='.next' \
  --exclude='dist' \
  --exclude='build' \
  --exclude='.env.local' \
  --exclude='.env.production' \
  --exclude='*.log' \
  --exclude='.DS_Store' \
  --exclude='coverage' \
  --exclude='.nyc_output' \
  --exclude='*.tgz' \
  --exclude='*.tar.gz' \
  ./ "$TEMP_DIR/"

# Create README for the download
cat > "$TEMP_DIR/DOWNLOAD_README.md" << 'EOF'
# SynapseAI - Universal AI Orchestration System

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- Redis 6+
- npm or yarn

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local` with your database and API credentials:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/synapseai"
   REDIS_URL="redis://localhost:6379"
   NEXTAUTH_SECRET="your-secret-key"
   OPENAI_API_KEY="your-openai-key"
   ```

3. **Set up the database:**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```

5. **Open your browser:**
   Navigate to `http://localhost:3000`

### Production Deployment

1. **Build the project:**
   ```bash
   npm run build
   ```

2. **Start production server:**
   ```bash
   npm start
   ```

### Key Features
- ✅ JWT + RBAC Authentication
- ✅ Real-time WebSocket (APIX Protocol)
- ✅ AI Agent Builder with Memory
- ✅ Tool Manager for API Functions
- ✅ Hybrid Tool-Agent Workflows
- ✅ Multi-Provider AI Support
- ✅ Session Management with Redis
- ✅ Knowledge Base (RAG)
- ✅ Embeddable Widgets
- ✅ Analytics Dashboard

### Architecture
- **Frontend:** Next.js 14 + Tailwind CSS + Shadcn UI
- **Backend:** NestJS + PostgreSQL + Redis
- **Real-time:** WebSocket with APIX protocol
- **AI:** Multi-provider support (OpenAI, Claude, Gemini, etc.)

### Support
For issues and questions, check the documentation in the `/docs` folder.
EOF

# Create a setup script
cat > "$TEMP_DIR/setup.sh" << 'EOF'
#!/bin/bash

echo "🔧 Setting up SynapseAI..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
if command -v yarn &> /dev/null; then
    yarn install
else
    npm install
fi

# Copy environment file if it doesn't exist
if [ ! -f ".env.local" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env.local
        echo "📝 Created .env.local from .env.example"
        echo "⚠️  Please edit .env.local with your database and API credentials"
    else
        echo "⚠️  No .env.example found. Please create .env.local manually"
    fi
fi

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env.local with your credentials"
echo "2. Set up PostgreSQL and Redis"
echo "3. Run: npm run db:migrate"
echo "4. Run: npm run dev"
echo ""
echo "🚀 Happy coding with SynapseAI!"
EOF

chmod +x "$TEMP_DIR/setup.sh"

# Create the archive
ARCHIVE_NAME="synapseai-project-$(date +%Y%m%d-%H%M%S).tar.gz"
echo "📁 Creating archive: $ARCHIVE_NAME"

tar -czf "$ARCHIVE_NAME" -C . "$TEMP_DIR"

# Clean up temp directory
rm -rf "$TEMP_DIR"

echo "✅ Download package created: $ARCHIVE_NAME"
echo ""
echo "📋 Package contents:"
echo "   - Complete SynapseAI source code"
echo "   - Setup script (setup.sh)"
echo "   - Download README with instructions"
echo "   - All configuration files"
echo ""
echo "🎯 To use:"
echo "   1. Download: $ARCHIVE_NAME"
echo "   2. Extract: tar -xzf $ARCHIVE_NAME"
echo "   3. Navigate: cd $TEMP_DIR"
echo "   4. Run: ./setup.sh"
echo ""
echo "🌟 Enjoy building with SynapseAI!"
