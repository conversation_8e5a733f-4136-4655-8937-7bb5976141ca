"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  TestTube,
  Play,
  Square,
  RotateCcw,
  Bug,
  CheckCircle,
  XCircle,
  Clock,
  Bot,
  Wrench,
  Network,
  Settings,
  Code,
  Terminal,
} from "lucide-react";
import { mockAuthContext } from "@/lib/auth-context";
import SandboxEnvironment from "@/components/sandbox/SandboxEnvironment";

interface SandboxConfig {
  id: string;
  name: string;
  description: string;
  targetType: "agent" | "tool" | "hybrid";
  targetId: string;
  environment: "development" | "staging" | "production";
  status: "idle" | "running" | "completed" | "error";
  lastRun: string;
  duration: string;
  testsPassed: number;
  testsTotal: number;
}

export default function SandboxPage() {
  const [activeTab, setActiveTab] = useState("environments");
  const [selectedConfig, setSelectedConfig] = useState<string>("");
  const { hasPermission } = mockAuthContext;

  const canRunSandbox = hasPermission("sandbox", "execute");
  const canCreateSandbox = hasPermission("sandbox", "create");

  const sandboxConfigs: SandboxConfig[] = [
    {
      id: "sandbox_1",
      name: "Customer Support Agent Test",
      description: "Test customer support agent responses and memory",
      targetType: "agent",
      targetId: "agent_1",
      environment: "development",
      status: "completed",
      lastRun: "2024-01-15T11:30:00Z",
      duration: "2m 34s",
      testsPassed: 8,
      testsTotal: 10,
    },
    {
      id: "sandbox_2",
      name: "Email Tool Integration",
      description: "Test email sending functionality and error handling",
      targetType: "tool",
      targetId: "tool_1",
      environment: "staging",
      status: "running",
      lastRun: "2024-01-15T11:45:00Z",
      duration: "1m 12s",
      testsPassed: 5,
      testsTotal: 8,
    },
    {
      id: "sandbox_3",
      name: "Sales Workflow End-to-End",
      description: "Complete sales workflow with lead qualification",
      targetType: "hybrid",
      targetId: "hybrid_1",
      environment: "development",
      status: "error",
      lastRun: "2024-01-15T10:15:00Z",
      duration: "45s",
      testsPassed: 2,
      testsTotal: 12,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-blue-500";
      case "completed":
        return "bg-green-500";
      case "error":
        return "bg-red-500";
      case "idle":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "agent":
        return Bot;
      case "tool":
        return Wrench;
      case "hybrid":
        return Network;
      default:
        return TestTube;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "running":
        return Play;
      case "completed":
        return CheckCircle;
      case "error":
        return XCircle;
      case "idle":
        return Clock;
      default:
        return Clock;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sandbox</h1>
          <p className="text-muted-foreground">
            Test and debug your AI agents, tools, and workflows in a safe environment
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {canCreateSandbox && (
            <Button>
              <TestTube className="h-4 w-4 mr-2" />
              New Sandbox
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sandboxes</CardTitle>
            <TestTube className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sandboxConfigs.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Running Tests</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sandboxConfigs.filter(s => s.status === "running").length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(
                (sandboxConfigs.reduce((acc, s) => acc + s.testsPassed, 0) /
                sandboxConfigs.reduce((acc, s) => acc + s.testsTotal, 0)) * 100
              )}%
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1m 47s</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="environments">Test Environments</TabsTrigger>
          <TabsTrigger value="runner">Test Runner</TabsTrigger>
          <TabsTrigger value="results">Test Results</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="environments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sandbox Environments</CardTitle>
              <CardDescription>
                Manage your test environments for different AI components
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {sandboxConfigs.map((config) => {
                  const TypeIcon = getTypeIcon(config.targetType);
                  const StatusIcon = getStatusIcon(config.status);
                  
                  return (
                    <Card key={config.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <TypeIcon className="h-4 w-4" />
                            <span className="font-medium">{config.name}</span>
                          </div>
                          <Badge variant="outline" className="flex items-center space-x-1">
                            <div className={`h-2 w-2 rounded-full ${getStatusColor(config.status)}`} />
                            <span className="capitalize">{config.status}</span>
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {config.description}
                        </p>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Environment:</span>
                            <Badge variant="secondary" className="capitalize">
                              {config.environment}
                            </Badge>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Last Run:</span>
                            <span>{new Date(config.lastRun).toLocaleTimeString()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Duration:</span>
                            <span>{config.duration}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Tests:</span>
                            <span className={config.testsPassed === config.testsTotal ? "text-green-600" : "text-yellow-600"}>
                              {config.testsPassed}/{config.testsTotal}
                            </span>
                          </div>
                        </div>

                        <div className="flex justify-between mt-4">
                          <div className="flex space-x-1">
                            {canRunSandbox && (
                              <>
                                <Button variant="ghost" size="sm">
                                  <Play className="h-3 w-3" />
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <Square className="h-3 w-3" />
                                </Button>
                                <Button variant="ghost" size="sm">
                                  <RotateCcw className="h-3 w-3" />
                                </Button>
                              </>
                            )}
                            <Button variant="ghost" size="sm">
                              <Bug className="h-3 w-3" />
                            </Button>
                          </div>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => setSelectedConfig(config.id)}
                          >
                            <Settings className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="runner" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Test Runner</CardTitle>
                  <CardDescription>
                    Run tests on your selected sandbox environment
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Select value={selectedConfig} onValueChange={setSelectedConfig}>
                    <SelectTrigger className="w-[250px]">
                      <SelectValue placeholder="Select sandbox environment" />
                    </SelectTrigger>
                    <SelectContent>
                      {sandboxConfigs.map((config) => (
                        <SelectItem key={config.id} value={config.id}>
                          {config.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {canRunSandbox && (
                    <Button>
                      <Play className="h-4 w-4 mr-2" />
                      Run Tests
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {selectedConfig ? (
                <SandboxEnvironment 
                  targetType="agent" 
                  targetId={selectedConfig}
                  config={{
                    id: selectedConfig,
                    name: sandboxConfigs.find(c => c.id === selectedConfig)?.name || "",
                    description: sandboxConfigs.find(c => c.id === selectedConfig)?.description || "",
                    environment: "development",
                    testSuites: [],
                    settings: {
                      timeout: 30000,
                      retries: 3,
                      parallel: false,
                      verbose: true,
                    }
                  }}
                />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <TestTube className="h-12 w-12 mx-auto mb-4" />
                  <p>Select a sandbox environment to start testing</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                View detailed results from your sandbox test runs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Terminal className="h-12 w-12 mx-auto mb-4" />
                <p>Test results will appear here after running tests</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sandbox Settings</CardTitle>
              <CardDescription>
                Configure global sandbox environment settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Default Timeout (ms)</label>
                  <Select defaultValue="30000">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10000">10 seconds</SelectItem>
                      <SelectItem value="30000">30 seconds</SelectItem>
                      <SelectItem value="60000">1 minute</SelectItem>
                      <SelectItem value="300000">5 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">Default Retries</label>
                  <Select defaultValue="3">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 retry</SelectItem>
                      <SelectItem value="3">3 retries</SelectItem>
                      <SelectItem value="5">5 retries</SelectItem>
                      <SelectItem value="10">10 retries</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">Parallel Execution</label>
                  <Select defaultValue="false">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="false">Sequential</SelectItem>
                      <SelectItem value="true">Parallel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>Save Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
