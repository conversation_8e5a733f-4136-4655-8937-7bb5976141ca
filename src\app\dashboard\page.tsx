"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Bot,
  Wrench,
  Network,
  BarChart3,
  Activity,
  Users,
  Zap,
  TrendingUp,
  TrendingDown,
  Clock,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Wifi,
  WifiOff,
  Shield,
  Plus,
  ArrowRight,
} from "lucide-react";
import { mockAuthContext } from "@/lib/auth-context";
import Link from "next/link";

interface DashboardMetric {
  title: string;
  value: string;
  change: number;
  trend: "up" | "down" | "stable";
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
}

interface QuickAction {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  color: string;
}

export default function DashboardOverview() {
  const [isConnected, setIsConnected] = useState(true);
  const { user, organization, hasPermission } = mockAuthContext;

  const metrics: DashboardMetric[] = [
    {
      title: "Active Agents",
      value: "24",
      change: 12.5,
      trend: "up",
      icon: Bot,
      href: "/dashboard/agents",
    },
    {
      title: "Tools Available",
      value: "42",
      change: 8.3,
      trend: "up",
      icon: Wrench,
      href: "/dashboard/tools",
    },
    {
      title: "Hybrid Workflows",
      value: "18",
      change: 15.2,
      trend: "up",
      icon: Network,
      href: "/dashboard/hybrids",
    },
    {
      title: "Active Sessions",
      value: "156",
      change: -2.1,
      trend: "down",
      icon: Activity,
      href: "/dashboard/sessions",
    },
    {
      title: "Total Executions",
      value: "12.4K",
      change: 23.8,
      trend: "up",
      icon: Zap,
      href: "/dashboard/analytics",
    },
    {
      title: "Monthly Cost",
      value: "$284.50",
      change: 5.7,
      trend: "up",
      icon: DollarSign,
      href: "/dashboard/analytics",
    },
  ];

  const quickActions: QuickAction[] = [
    {
      title: "Create Agent",
      description: "Build a new AI agent with memory",
      icon: Bot,
      href: "/dashboard/agents",
      color: "bg-blue-500",
    },
    {
      title: "Add Tool",
      description: "Create a new API tool",
      icon: Wrench,
      href: "/dashboard/tools",
      color: "bg-green-500",
    },
    {
      title: "Build Hybrid",
      description: "Combine agents and tools",
      icon: Network,
      href: "/dashboard/hybrids",
      color: "bg-purple-500",
    },
    {
      title: "View Analytics",
      description: "Check performance metrics",
      icon: BarChart3,
      href: "/dashboard/analytics",
      color: "bg-orange-500",
    },
  ];

  const recentActivity = [
    {
      id: 1,
      type: "agent",
      title: "Customer Support Agent executed",
      time: "2 minutes ago",
      status: "success",
    },
    {
      id: 2,
      type: "tool",
      title: "Email Tool created",
      time: "5 minutes ago",
      status: "success",
    },
    {
      id: 3,
      type: "hybrid",
      title: "Data Analysis Workflow updated",
      time: "12 minutes ago",
      status: "success",
    },
    {
      id: 4,
      type: "session",
      title: "Session timeout occurred",
      time: "18 minutes ago",
      status: "warning",
    },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.name}. Here's what's happening with your AI
            platform.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? (
              <Wifi className="h-3 w-3 mr-1" />
            ) : (
              <WifiOff className="h-3 w-3 mr-1" />
            )}
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <Shield className="h-3 w-3" />
            {user?.role}
          </Badge>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          const canView = metric.href
            ? hasPermission(metric.href.split("/")[2], "read")
            : true;

          if (!canView) return null;

          return (
            <Card key={metric.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                  {metric.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : metric.trend === "down" ? (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  ) : (
                    <div className="h-3 w-3" />
                  )}
                  <span
                    className={
                      metric.trend === "up"
                        ? "text-green-500"
                        : metric.trend === "down"
                        ? "text-red-500"
                        : ""
                    }
                  >
                    {metric.change > 0 ? "+" : ""}
                    {metric.change}%
                  </span>
                  <span>from last month</span>
                </div>
                {metric.href && (
                  <Link href={metric.href}>
                    <Button variant="ghost" size="sm" className="mt-2 p-0 h-auto">
                      View details <ArrowRight className="h-3 w-3 ml-1" />
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Get started quickly with these common tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action) => {
              const Icon = action.icon;
              const canAccess = hasPermission(
                action.href.split("/")[2],
                "create"
              );

              if (!canAccess) return null;

              return (
                <Link key={action.title} href={action.href}>
                  <Card className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`flex h-10 w-10 items-center justify-center rounded-lg ${action.color}`}
                        >
                          <Icon className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-medium">{action.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest events across your platform</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3">
                  <div
                    className={`flex h-8 w-8 items-center justify-center rounded-full ${
                      activity.status === "success"
                        ? "bg-green-100 text-green-600"
                        : activity.status === "warning"
                        ? "bg-yellow-100 text-yellow-600"
                        : "bg-red-100 text-red-600"
                    }`}
                  >
                    {activity.status === "success" ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{activity.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>Current platform health</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">API Response Time</span>
                <span className="text-sm text-green-600">142ms</span>
              </div>
              <Progress value={85} className="h-2" />
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">System Load</span>
                <span className="text-sm text-yellow-600">68%</span>
              </div>
              <Progress value={68} className="h-2" />
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Memory Usage</span>
                <span className="text-sm text-green-600">45%</span>
              </div>
              <Progress value={45} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
