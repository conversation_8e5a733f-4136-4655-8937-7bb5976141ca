/* React Flow Styles */
.react-flow {
  width: 100%;
  height: 100%;
}

.react-flow__node {
  cursor: pointer;
}

.react-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

.react-flow__edge {
  cursor: pointer;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #3b82f6;
  stroke-width: 3;
}

.react-flow__handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  border-radius: 50%;
  cursor: crosshair;
}

.react-flow__handle-connecting {
  background: #3b82f6;
}

.react-flow__handle-valid {
  background: #10b981;
}

.react-flow__handle-invalid {
  background: #ef4444;
}

.react-flow__connection-line {
  stroke: #3b82f6;
  stroke-width: 2;
  stroke-dasharray: 5;
}

.react-flow__controls {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: white;
  border: 1px solid #e5e7eb;
}

.react-flow__controls button {
  border: none;
  background: transparent;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.react-flow__controls button:hover {
  background: #f3f4f6;
}

.react-flow__minimap {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.react-flow__background {
  background-color: #f9fafb;
}

.react-flow__background.dots {
  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);
  background-size: 20px 20px;
}

.react-flow__background.lines {
  background-image: 
    linear-gradient(to right, #e5e7eb 1px, transparent 1px),
    linear-gradient(to bottom, #e5e7eb 1px, transparent 1px);
  background-size: 20px 20px;
}

.react-flow__panel {
  z-index: 10;
}

/* Custom node animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom edge label styles */
.react-flow__edge-label {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  color: #374151;
}

/* Node status indicators */
.node-status-idle {
  border-color: #d1d5db;
}

.node-status-running {
  border-color: #3b82f6;
  animation: pulse 2s infinite;
}

.node-status-completed {
  border-color: #10b981;
}

.node-status-failed {
  border-color: #ef4444;
}

.node-status-paused {
  border-color: #f59e0b;
}

/* Connection validation styles */
.react-flow__handle.connectable {
  cursor: crosshair;
}

.react-flow__handle.connectable:hover {
  transform: scale(1.2);
  transition: transform 0.2s;
}

/* Custom scrollbar for better UX */
.react-flow::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.react-flow::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.react-flow::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.react-flow::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
