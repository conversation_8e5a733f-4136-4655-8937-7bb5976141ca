// Production API Routes for Individual Agent Management
// CRUD operations with multi-tenant security and real-time updates

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface UpdateAgentRequest {
  name?: string;
  description?: string;
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  templateId?: string;
  isPublic?: boolean;
  version?: string;
  status?: "active" | "inactive" | "archived";
  metadata?: Record<string, any>;
}

// GET /api/agents/[id] - Get specific agent
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "agents", "read")) {
      return NextResponse.json(
        { error: "Insufficient permissions to read agents" },
        { status: 403 },
      );
    }

    const agentId = params.id;
    if (!agentId) {
      return NextResponse.json(
        { error: "Agent ID is required" },
        { status: 400 },
      );
    }

    // Fetch agent with multi-tenant security
    const agent = await db.getAgent(agentId, payload.org);
    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Create session for this API call
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      { action: "get", agentId, agent },
      {
        tags: ["api", "agents", "get"],
        conversationId: `api-get-${Date.now()}`,
      },
    );

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-viewed-${agentId}`,
        type: "viewed",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        agent,
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error fetching agent:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// PUT /api/agents/[id] - Update agent
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "agents", "update")) {
      return NextResponse.json(
        { error: "Insufficient permissions to update agents" },
        { status: 403 },
      );
    }

    const agentId = params.id;
    if (!agentId) {
      return NextResponse.json(
        { error: "Agent ID is required" },
        { status: 400 },
      );
    }

    // Check if agent exists and user has access
    const existingAgent = await db.getAgent(agentId, payload.org);
    if (!existingAgent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Parse and validate request body
    const body: UpdateAgentRequest = await request.json();

    // Validation
    if (body.name !== undefined && !body.name.trim()) {
      return NextResponse.json(
        { error: "Agent name cannot be empty" },
        { status: 400 },
      );
    }

    if (
      body.temperature !== undefined &&
      (body.temperature < 0 || body.temperature > 2)
    ) {
      return NextResponse.json(
        { error: "Temperature must be between 0 and 2" },
        { status: 400 },
      );
    }

    if (
      body.maxTokens !== undefined &&
      (body.maxTokens < 1 || body.maxTokens > 8192)
    ) {
      return NextResponse.json(
        { error: "Max tokens must be between 1 and 8192" },
        { status: 400 },
      );
    }

    // Prepare update data
    const updateData: Partial<typeof existingAgent> = {};
    if (body.name !== undefined) updateData.name = body.name.trim();
    if (body.description !== undefined)
      updateData.description = body.description.trim();
    if (body.provider !== undefined) updateData.provider = body.provider;
    if (body.model !== undefined) updateData.model = body.model;
    if (body.temperature !== undefined)
      updateData.temperature = body.temperature;
    if (body.maxTokens !== undefined) updateData.maxTokens = body.maxTokens;
    if (body.systemPrompt !== undefined)
      updateData.systemPrompt = body.systemPrompt;
    if (body.templateId !== undefined) updateData.templateId = body.templateId;
    if (body.isPublic !== undefined) updateData.isPublic = body.isPublic;
    if (body.version !== undefined) updateData.version = body.version;
    if (body.status !== undefined) updateData.status = body.status;
    if (body.metadata !== undefined)
      updateData.metadata = { ...existingAgent.metadata, ...body.metadata };

    // Update agent
    const updatedAgent = await db.updateAgent(agentId, payload.org, updateData);

    // Create session for this update
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      { action: "update", agentId, changes: updateData, updatedAgent },
      {
        tags: ["api", "agents", "update"],
        conversationId: `api-update-${Date.now()}`,
      },
    );

    // Update template usage if template changed
    if (body.templateId && body.templateId !== existingAgent.templateId) {
      try {
        await db.updateTemplateUsage(body.templateId, payload.org);
      } catch (templateError) {
        console.warn("Failed to update template usage:", templateError);
      }
    }

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-updated-${agentId}`,
        type: "updated",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId,
          agent: updatedAgent,
          changes: updateData,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        agent: updatedAgent,
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error updating agent:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// DELETE /api/agents/[id] - Delete (archive) agent
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "agents", "delete")) {
      return NextResponse.json(
        { error: "Insufficient permissions to delete agents" },
        { status: 403 },
      );
    }

    const agentId = params.id;
    if (!agentId) {
      return NextResponse.json(
        { error: "Agent ID is required" },
        { status: 400 },
      );
    }

    // Check if agent exists and user has access
    const existingAgent = await db.getAgent(agentId, payload.org);
    if (!existingAgent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Soft delete (archive) the agent
    await db.deleteAgent(agentId, payload.org);

    // Create session for this deletion
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      { action: "delete", agentId, deletedAgent: existingAgent },
      {
        tags: ["api", "agents", "delete"],
        conversationId: `api-delete-${Date.now()}`,
      },
    );

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-deleted-${agentId}`,
        type: "deleted",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        message: "Agent deleted successfully",
        agentId,
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error deleting agent:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
