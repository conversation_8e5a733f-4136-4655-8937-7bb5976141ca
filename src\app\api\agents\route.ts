// Production API Routes for Agent Management
// Multi-tenant, RBAC-secured endpoints with comprehensive error handling

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";

interface CreateAgentRequest {
  name: string;
  description: string;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  templateId?: string;
  isPublic: boolean;
  version: string;
  metadata?: Record<string, any>;
}

interface ListAgentsQuery {
  page?: string;
  limit?: string;
  provider?: string;
  status?: string;
  isPublic?: string;
  userId?: string;
}

// GET /api/agents - List agents with multi-tenant filtering
export async function GET(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "agents", "read")) {
      return NextResponse.json(
        { error: "Insufficient permissions to read agents" },
        { status: 403 },
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const query: ListAgentsQuery = {
      page: searchParams.get("page") || "1",
      limit: searchParams.get("limit") || "20",
      provider: searchParams.get("provider") || undefined,
      status: searchParams.get("status") || undefined,
      isPublic: searchParams.get("isPublic") || undefined,
      userId: searchParams.get("userId") || undefined,
    };

    const page = parseInt(query.page);
    const limit = Math.min(parseInt(query.limit), 100); // Max 100 per page
    const offset = (page - 1) * limit;

    // Build filters with organization scoping
    const filters = {
      limit,
      offset,
      ...(query.provider && { provider: query.provider }),
      ...(query.status && { status: query.status }),
      ...(query.isPublic !== undefined && {
        isPublic: query.isPublic === "true",
      }),
      ...(query.userId && { userId: query.userId }),
    };

    // Fetch agents with multi-tenant security
    const { agents, total } = await db.listAgents(payload.org, filters);

    // Create session for this API call
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      { action: "list", filters, results: agents.length },
      {
        tags: ["api", "agents", "list"],
        conversationId: `api-${Date.now()}`,
      },
    );

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agents-listed-${Date.now()}`,
        type: "agents_listed",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          count: agents.length,
          total,
          filters,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
      // Don't fail the request if APIX is unavailable
    }

    return NextResponse.json({
      success: true,
      data: {
        agents,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error listing agents:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// POST /api/agents - Create new agent
export async function POST(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "agents", "create")) {
      return NextResponse.json(
        { error: "Insufficient permissions to create agents" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body: CreateAgentRequest = await request.json();

    // Validation
    if (!body.name?.trim()) {
      return NextResponse.json(
        { error: "Agent name is required" },
        { status: 400 },
      );
    }

    if (!body.provider || !body.model) {
      return NextResponse.json(
        { error: "Provider and model are required" },
        { status: 400 },
      );
    }

    if (body.temperature < 0 || body.temperature > 2) {
      return NextResponse.json(
        { error: "Temperature must be between 0 and 2" },
        { status: 400 },
      );
    }

    if (body.maxTokens < 1 || body.maxTokens > 8192) {
      return NextResponse.json(
        { error: "Max tokens must be between 1 and 8192" },
        { status: 400 },
      );
    }

    // Check quota limits (this would typically check against organization quotas)
    const { agents: existingAgents } = await db.listAgents(payload.org, {
      limit: 1,
    });
    // In a real implementation, you'd check against organization.quotas.agents

    // Create agent with organization scoping
    const agentData = {
      organizationId: payload.org,
      userId: payload.sub,
      name: body.name.trim(),
      description: body.description?.trim() || "",
      provider: body.provider,
      model: body.model,
      temperature: body.temperature,
      maxTokens: body.maxTokens,
      systemPrompt: body.systemPrompt || "You are a helpful AI assistant.",
      templateId: body.templateId,
      isPublic: body.isPublic || false,
      version: body.version || "1.0.0",
      metadata: body.metadata || {},
    };

    const agent = await db.createAgent(agentData);

    // Create session for this creation
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "agents",
      "user",
      { action: "create", agentId: agent.id, agentData },
      {
        tags: ["api", "agents", "create"],
        conversationId: `api-create-${Date.now()}`,
      },
    );

    // Update template usage if template was used
    if (body.templateId) {
      try {
        await db.updateTemplateUsage(body.templateId, payload.org);
      } catch (templateError) {
        console.warn("Failed to update template usage:", templateError);
        // Don't fail agent creation if template update fails
      }
    }

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `agent-created-${agent.id}`,
        type: "created",
        module: "agents",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          agentId: agent.id,
          agent,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
      // Don't fail the request if APIX is unavailable
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          agent,
          sessionId,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error creating agent:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
