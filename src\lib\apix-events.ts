// APIX Events for Tool Manager and Tool-Agent Hybrid System
// Extends existing APIX event system with tool and hybrid workflow events

import { APXEvent } from "./apix-client";

// Tool Manager Events
export interface ToolCreatedEvent extends APXEvent {
  type: "tool_created";
  module: "tools";
  data: {
    toolId: string;
    name: string;
    category: string;
    apiEndpoint: string;
    schema: any;
    createdBy: string;
  };
}

export interface ToolExecutedEvent extends APXEvent {
  type: "tool_executed";
  module: "tools";
  data: {
    toolId: string;
    executionId: string;
    input: any;
    output: any;
    duration: number;
    success: boolean;
    cost: number;
    error?: string;
  };
}

export interface ToolChainedEvent extends APXEvent {
  type: "tool_chained";
  module: "tools";
  data: {
    chainId: string;
    tools: string[];
    executionOrder: number[];
    totalDuration: number;
    success: boolean;
  };
}

export interface ToolQuotaExceededEvent extends APXEvent {
  type: "tool_quota_exceeded";
  module: "tools";
  data: {
    toolId: string;
    quotaType: "executions" | "cost" | "rate_limit";
    currentUsage: number;
    limit: number;
    resetTime?: string;
  };
}

// Hybrid System Events
export interface HybridStartedEvent extends APXEvent {
  type: "hybrid_started";
  module: "hybrids";
  data: {
    hybridId: string;
    workflowId: string;
    agentId: string;
    tools: string[];
    input: any;
    expectedSteps: number;
  };
}

export interface HybridStepCompletedEvent extends APXEvent {
  type: "hybrid_step_completed";
  module: "hybrids";
  data: {
    hybridId: string;
    stepId: string;
    stepType: "agent_reasoning" | "tool_execution" | "conditional_logic";
    stepNumber: number;
    output: any;
    duration: number;
    nextStep?: string;
  };
}

export interface HybridFinishedEvent extends APXEvent {
  type: "hybrid_finished";
  module: "hybrids";
  data: {
    hybridId: string;
    totalSteps: number;
    totalDuration: number;
    finalOutput: any;
    success: boolean;
    cost: number;
    error?: string;
  };
}

export interface HybridExecutionFailedEvent extends APXEvent {
  type: "hybrid_execution_failed";
  module: "hybrids";
  data: {
    hybridId: string;
    failedStep: string;
    error: string;
    partialOutput?: any;
    fallbackTriggered: boolean;
    fallbackStrategy?: string;
  };
}

// Provider Management Events
export interface ProviderSelectedEvent extends APXEvent {
  type: "provider_selected";
  module: "providers";
  data: {
    providerId: string;
    model: string;
    reason: "cost" | "performance" | "availability" | "capability";
    score: number;
    alternatives: Array<{ providerId: string; score: number }>;
  };
}

export interface ProviderSwitchedEvent extends APXEvent {
  type: "provider_switched";
  module: "providers";
  data: {
    fromProvider: string;
    toProvider: string;
    reason: "failover" | "optimization" | "user_preference";
    executionId: string;
  };
}

export interface ProviderFailedEvent extends APXEvent {
  type: "provider_failed";
  module: "providers";
  data: {
    providerId: string;
    error: string;
    failureType: "timeout" | "rate_limit" | "api_error" | "quota_exceeded";
    circuitBreakerTriggered: boolean;
    fallbackProvider?: string;
  };
}

export interface ProviderOptimizedEvent extends APXEvent {
  type: "provider_optimized";
  module: "providers";
  data: {
    providerId: string;
    optimizationType: "cost" | "performance" | "reliability";
    oldScore: number;
    newScore: number;
    impact: string;
  };
}

// SDK Events
export interface SDKConnectionEvent extends APXEvent {
  type: "sdk_connection";
  module: "sdk";
  data: {
    clientId: string;
    sdkVersion: string;
    language: "typescript" | "python" | "rest";
    connectionType: "websocket" | "http";
    authenticated: boolean;
  };
}

export interface SDKAuthenticationEvent extends APXEvent {
  type: "sdk_authentication";
  module: "sdk";
  data: {
    clientId: string;
    success: boolean;
    method: "api_key" | "jwt" | "oauth";
    organizationId: string;
    permissions: string[];
  };
}

export interface SDKRateLimitedEvent extends APXEvent {
  type: "sdk_rate_limited";
  module: "sdk";
  data: {
    clientId: string;
    endpoint: string;
    limit: number;
    remaining: number;
    resetTime: string;
    retryAfter: number;
  };
}

// Widget Generator Events
export interface WidgetCreatedEvent extends APXEvent {
  type: "widget_created";
  module: "widgets";
  data: {
    widgetId: string;
    name: string;
    type: "agent" | "tool" | "hybrid";
    sourceId: string;
    theme: string;
    embedFormat: "javascript" | "iframe" | "wordpress" | "shopify";
    customization: any;
    createdBy: string;
  };
}

export interface WidgetEmbeddedEvent extends APXEvent {
  type: "widget_embedded";
  module: "widgets";
  data: {
    widgetId: string;
    embedUrl: string;
    referrer: string;
    userAgent: string;
    embedFormat: string;
    customization?: any;
  };
}

export interface WidgetExecutedEvent extends APXEvent {
  type: "widget_executed";
  module: "widgets";
  data: {
    widgetId: string;
    executionId: string;
    input: any;
    output: any;
    duration: number;
    success: boolean;
    cost: number;
    embedUrl: string;
    userInteraction: any;
  };
}

// Analytics Dashboard Events
export interface AnalyticsUpdatedEvent extends APXEvent {
  type: "analytics_updated";
  module: "analytics";
  data: {
    metricType: string;
    value: number;
    timestamp: string;
    dimensions: Record<string, any>;
    aggregationType: "sum" | "avg" | "count" | "max" | "min";
  };
}

export interface DashboardViewedEvent extends APXEvent {
  type: "dashboard_viewed";
  module: "analytics";
  data: {
    dashboardId: string;
    viewType: "overview" | "agents" | "tools" | "hybrids" | "widgets";
    filters: Record<string, any>;
    timeRange: {
      start: string;
      end: string;
    };
  };
}

export interface ReportGeneratedEvent extends APXEvent {
  type: "report_generated";
  module: "analytics";
  data: {
    reportId: string;
    reportType: "performance" | "usage" | "cost" | "conversion";
    format: "pdf" | "csv" | "json";
    filters: Record<string, any>;
    generatedBy: string;
    downloadUrl: string;
  };
}

// Sandbox Testing Events
export interface SandboxCreatedEvent extends APXEvent {
  type: "sandbox_created";
  module: "sandbox";
  data: {
    sandboxId: string;
    name: string;
    type: "agent" | "tool" | "hybrid" | "integration";
    configuration: any;
    collaborators: string[];
    createdBy: string;
  };
}

export interface TestExecutedEvent extends APXEvent {
  type: "test_executed";
  module: "sandbox";
  data: {
    sandboxId: string;
    testId: string;
    testType: "unit" | "integration" | "load" | "security";
    input: any;
    output: any;
    duration: number;
    success: boolean;
    assertions: Array<{
      name: string;
      passed: boolean;
      expected: any;
      actual: any;
    }>;
    performance: {
      memory: number;
      cpu: number;
      latency: number;
    };
  };
}

export interface DebugSessionStartedEvent extends APXEvent {
  type: "debug_session_started";
  module: "sandbox";
  data: {
    sandboxId: string;
    sessionId: string;
    debugType: "step-by-step" | "breakpoint" | "trace";
    target: {
      type: "agent" | "tool" | "hybrid";
      id: string;
    };
    collaborators: string[];
  };
}

// Union type for all new events
export type ToolManagerEvent =
  | ToolCreatedEvent
  | ToolExecutedEvent
  | ToolChainedEvent
  | ToolQuotaExceededEvent;

export type HybridSystemEvent =
  | HybridStartedEvent
  | HybridStepCompletedEvent
  | HybridFinishedEvent
  | HybridExecutionFailedEvent;

export type ProviderEvent =
  | ProviderSelectedEvent
  | ProviderSwitchedEvent
  | ProviderFailedEvent
  | ProviderOptimizedEvent;

export type SDKEvent =
  | SDKConnectionEvent
  | SDKAuthenticationEvent
  | SDKRateLimitedEvent;

export type WidgetEvent =
  | WidgetCreatedEvent
  | WidgetEmbeddedEvent
  | WidgetExecutedEvent;

export type AnalyticsEvent =
  | AnalyticsUpdatedEvent
  | DashboardViewedEvent
  | ReportGeneratedEvent;

export type SandboxEvent =
  | SandboxCreatedEvent
  | TestExecutedEvent
  | DebugSessionStartedEvent;

export type ExtendedAPXEvent =
  | APXEvent
  | ToolManagerEvent
  | HybridSystemEvent
  | ProviderEvent
  | SDKEvent
  | WidgetEvent
  | AnalyticsEvent
  | SandboxEvent;

// Event type guards
export function isToolEvent(
  event: ExtendedAPXEvent,
): event is ToolManagerEvent {
  return event.module === "tools";
}

export function isHybridEvent(
  event: ExtendedAPXEvent,
): event is HybridSystemEvent {
  return event.module === "hybrids";
}

export function isProviderEvent(
  event: ExtendedAPXEvent,
): event is ProviderEvent {
  return event.module === "providers";
}

export function isSDKEvent(event: ExtendedAPXEvent): event is SDKEvent {
  return event.module === "sdk";
}

export function isWidgetEvent(event: ExtendedAPXEvent): event is WidgetEvent {
  return event.module === "widgets";
}

export function isAnalyticsEvent(
  event: ExtendedAPXEvent,
): event is AnalyticsEvent {
  return event.module === "analytics";
}

export function isSandboxEvent(event: ExtendedAPXEvent): event is SandboxEvent {
  return event.module === "sandbox";
}

// Event factory functions
export function createToolExecutedEvent(
  organizationId: string,
  userId: string,
  data: ToolExecutedEvent["data"],
): ToolExecutedEvent {
  return {
    id: `tool-executed-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: "tool_executed",
    module: "tools",
    organizationId,
    userId,
    timestamp: new Date().toISOString(),
    data,
    version: 1,
  };
}

export function createHybridStepCompletedEvent(
  organizationId: string,
  userId: string,
  data: HybridStepCompletedEvent["data"],
): HybridStepCompletedEvent {
  return {
    id: `hybrid-step-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: "hybrid_step_completed",
    module: "hybrids",
    organizationId,
    userId,
    timestamp: new Date().toISOString(),
    data,
    version: 1,
  };
}

export function createProviderSelectedEvent(
  organizationId: string,
  userId: string,
  data: ProviderSelectedEvent["data"],
): ProviderSelectedEvent {
  return {
    id: `provider-selected-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: "provider_selected",
    module: "providers",
    organizationId,
    userId,
    timestamp: new Date().toISOString(),
    data,
    version: 1,
  };
}

export function createWidgetCreatedEvent(
  organizationId: string,
  userId: string,
  data: WidgetCreatedEvent["data"],
): WidgetCreatedEvent {
  return {
    id: `widget-created-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: "widget_created",
    module: "widgets",
    organizationId,
    userId,
    timestamp: new Date().toISOString(),
    data,
    version: 1,
  };
}

export function createAnalyticsUpdatedEvent(
  organizationId: string,
  userId: string,
  data: AnalyticsUpdatedEvent["data"],
): AnalyticsUpdatedEvent {
  return {
    id: `analytics-updated-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: "analytics_updated",
    module: "analytics",
    organizationId,
    userId,
    timestamp: new Date().toISOString(),
    data,
    version: 1,
  };
}

export function createSandboxCreatedEvent(
  organizationId: string,
  userId: string,
  data: SandboxCreatedEvent["data"],
): SandboxCreatedEvent {
  return {
    id: `sandbox-created-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    type: "sandbox_created",
    module: "sandbox",
    organizationId,
    userId,
    timestamp: new Date().toISOString(),
    data,
    version: 1,
  };
}
