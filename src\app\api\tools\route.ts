// Production API Routes for Tool Management
// Multi-tenant, RBAC-secured endpoints with comprehensive error handling

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/database";
import { verifyToken, hasPermission } from "@/lib/auth-context";
import { getAPXClient } from "@/lib/apix-client";
import { getSessionManager } from "@/lib/session-manager";
import { createToolExecutedEvent } from "@/lib/apix-events";

interface CreateToolRequest {
  name: string;
  description: string;
  category: string;
  apiEndpoint: string;
  httpMethod: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers: Record<string, string>;
  parameters: Array<{
    name: string;
    type: "string" | "number" | "boolean" | "array" | "object";
    description: string;
    required: boolean;
    defaultValue?: any;
    validation?: {
      min?: number;
      max?: number;
      pattern?: string;
      enum?: string[];
    };
  }>;
  credentials: Array<{
    name: string;
    type: "api_key" | "bearer_token" | "basic_auth" | "oauth2";
    description: string;
    required: boolean;
    encrypted: boolean;
  }>;
  responseSchema: any;
  timeout: number;
  retryAttempts: number;
  rateLimiting: {
    enabled: boolean;
    requestsPerMinute: number;
    burstLimit: number;
  };
  caching: {
    enabled: boolean;
    ttl: number;
    keyPattern: string;
  };
  isPublic: boolean;
  version: string;
  tags: string[];
}

interface ListToolsQuery {
  page?: string;
  limit?: string;
  category?: string;
  status?: string;
  isPublic?: string;
  userId?: string;
}

interface ExecuteToolRequest {
  toolId: string;
  input: Record<string, any>;
  agentId?: string;
  workflowId?: string;
  context?: Record<string, any>;
}

// GET /api/tools - List tools with multi-tenant filtering
export async function GET(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "tools", "read")) {
      return NextResponse.json(
        { error: "Insufficient permissions to read tools" },
        { status: 403 },
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const query: ListToolsQuery = {
      page: searchParams.get("page") || "1",
      limit: searchParams.get("limit") || "20",
      category: searchParams.get("category") || undefined,
      status: searchParams.get("status") || undefined,
      isPublic: searchParams.get("isPublic") || undefined,
      userId: searchParams.get("userId") || undefined,
    };

    const page = parseInt(query.page);
    const limit = Math.min(parseInt(query.limit), 100); // Max 100 per page
    const offset = (page - 1) * limit;

    // Build filters with organization scoping
    const filters = {
      limit,
      offset,
      ...(query.category && { category: query.category }),
      ...(query.status && { status: query.status }),
      ...(query.isPublic !== undefined && {
        isPublic: query.isPublic === "true",
      }),
      ...(query.userId && { userId: query.userId }),
    };

    // Fetch tools with multi-tenant security
    const { tools, total } = await db.listTools(payload.org, filters);

    // Create session for this API call
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "tools",
      "user",
      { action: "list", filters, results: tools.length },
      {
        tags: ["api", "tools", "list"],
        conversationId: `api-${Date.now()}`,
      },
    );

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `tools-listed-${Date.now()}`,
        type: "tools_listed",
        module: "tools",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          count: tools.length,
          total,
          filters,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json({
      success: true,
      data: {
        tools,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        sessionId,
      },
    });
  } catch (error) {
    console.error("Error listing tools:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// POST /api/tools - Create new tool
export async function POST(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const authHeader = request.headers.get("authorization");
    if (!authHeader?.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid authorization header" },
        { status: 401 },
      );
    }

    const token = authHeader.substring(7);
    const payload = await verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 },
      );
    }

    // Check permissions
    if (!hasPermission(payload.role, payload.permissions, "tools", "create")) {
      return NextResponse.json(
        { error: "Insufficient permissions to create tools" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body: CreateToolRequest = await request.json();

    // Validation
    if (!body.name?.trim()) {
      return NextResponse.json(
        { error: "Tool name is required" },
        { status: 400 },
      );
    }

    if (!body.apiEndpoint?.trim()) {
      return NextResponse.json(
        { error: "API endpoint is required" },
        { status: 400 },
      );
    }

    if (!body.httpMethod) {
      return NextResponse.json(
        { error: "HTTP method is required" },
        { status: 400 },
      );
    }

    // Create tool with organization scoping
    const toolData = {
      organizationId: payload.org,
      userId: payload.sub,
      name: body.name.trim(),
      description: body.description?.trim() || "",
      category: body.category || "api",
      apiEndpoint: body.apiEndpoint.trim(),
      httpMethod: body.httpMethod,
      headers: body.headers || { "Content-Type": "application/json" },
      parameters: body.parameters || [],
      credentials: body.credentials || [],
      responseSchema: body.responseSchema || {},
      timeout: body.timeout || 30000,
      retryAttempts: body.retryAttempts || 3,
      rateLimiting: body.rateLimiting || {
        enabled: false,
        requestsPerMinute: 60,
        burstLimit: 10,
      },
      caching: body.caching || {
        enabled: false,
        ttl: 300,
        keyPattern: "{{endpoint}}-{{params}}",
      },
      isPublic: body.isPublic || false,
      version: body.version || "1.0.0",
      tags: body.tags || [],
      status: "active" as const,
      metadata: {
        createdVia: "api",
        builderVersion: "2.0.0",
      },
    };

    const tool = await db.createTool(toolData);

    // Create session for this creation
    const sessionManager = getSessionManager(payload.org);
    const sessionId = await sessionManager.createSession(
      "tools",
      "user",
      { action: "create", toolId: tool.id, toolData },
      {
        tags: ["api", "tools", "create"],
        conversationId: `api-create-${Date.now()}`,
      },
    );

    // Send real-time event via APIX
    try {
      const apixClient = getAPXClient(payload.org, payload.sub, token);
      await apixClient.sendEvent({
        id: `tool-created-${tool.id}`,
        type: "tool_created",
        module: "tools",
        organizationId: payload.org,
        userId: payload.sub,
        timestamp: new Date().toISOString(),
        data: {
          toolId: tool.id,
          tool,
          sessionId,
        },
        version: 1,
      });
    } catch (apixError) {
      console.warn("Failed to send APIX event:", apixError);
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          tool,
          sessionId,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error creating tool:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
